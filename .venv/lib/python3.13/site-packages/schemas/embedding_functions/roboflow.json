{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Roboflow Embedding Function Schema", "description": "Schema for the roboflow embedding function configuration", "version": "1.0.0", "type": "object", "properties": {"api_url": {"type": "string", "description": "Parameter api_url for the roboflow embedding function"}, "api_key_env_var": {"type": "string", "description": "Parameter api_key_env_var for the roboflow embedding function"}}, "required": ["api_key_env_var", "api_url"], "additionalProperties": false}