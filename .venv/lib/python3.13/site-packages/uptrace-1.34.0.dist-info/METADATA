Metadata-Version: 2.2
Name: uptrace
Version: 1.34.0
Summary: OpenTelemetry Python distribution for Uptrace
Home-page: https://uptrace.dev
Author: Uptrace.dev
Author-email: <EMAIL>
License: BSD
Platform: any
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Typing :: Typed
Requires-Python: >=3.8
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: opentelemetry-api~=1.34.1
Requires-Dist: opentelemetry-sdk~=1.34.1
Requires-Dist: opentelemetry-exporter-otlp~=1.34.1
Requires-Dist: opentelemetry-instrumentation~=0.55b1

# Uptrace for Python

![build workflow](https://github.com/uptrace/uptrace-python/actions/workflows/build.yml/badge.svg)
[![Documentation](https://img.shields.io/badge/uptrace-documentation-informational)](https://uptrace.dev/get/opentelemetry-python)
[![Chat](https://img.shields.io/badge/-telegram-red?color=white&logo=telegram&logoColor=black)](https://t.me/uptrace)

<a href="https://uptrace.dev/get/opentelemetry-python">
  <img src="https://uptrace.dev/devicon/python-original.svg" height="200px" />
</a>

## Introduction

uptrace-python is a thin wrapper over
[opentelemetry-python](https://github.com/open-telemetry/opentelemetry-python) that exports
[traces](https://uptrace.dev/opentelemetry/distributed-tracing),
[metrics](https://uptrace.dev/opentelemetry/metrics), and logs to Uptrace.

- [Documentation](https://uptrace.dev/get/opentelemetry-python)
- [Examples](example)
- [OpenTelemetry Django](https://uptrace.dev/guides/opentelemetry-django)
- [OpenTelemetry Flask](https://uptrace.dev/guides/opentelemetry-flask)
- [OpenTelemetry FastAPI](https://uptrace.dev/guides/opentelemetry-fastapi)
- [OpenTelemetry SQLAlchemy](https://uptrace.dev/guides/opentelemetry-sqlalchemy)
