import{r as E,u as he,j as h,L as _e,A as Fe,a as $e,b as xe,P as j,c as ve,T as Pe,d as Me,e as B,f as Ve,g as Ie,h as re,i as ye,k as Ee,l as De,m as He}from"./index-B28WSRhf.js";/**
   * table-core
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function I(e,o){return typeof e=="function"?e(o):e}function v(e,o){return n=>{o.setState(t=>({...t,[e]:I(n,t[e])}))}}function O(e){return e instanceof Function}function Ae(e){return Array.isArray(e)&&e.every(o=>typeof o=="number")}function Ge(e,o){const n=[],t=i=>{i.forEach(r=>{n.push(r);const l=o(r);l!=null&&l.length&&t(l)})};return t(e),n}function m(e,o,n){let t=[],i;return r=>{let l;n.key&&n.debug&&(l=Date.now());const u=e(r);if(!(u.length!==t.length||u.some((d,c)=>t[c]!==d)))return i;t=u;let a;if(n.key&&n.debug&&(a=Date.now()),i=o(...u),n==null||n.onChange==null||n.onChange(i),n.key&&n.debug&&n!=null&&n.debug()){const d=Math.round((Date.now()-l)*100)/100,c=Math.round((Date.now()-a)*100)/100,f=c/16,g=(p,S)=>{for(p=String(p);p.length<S;)p=" "+p;return p};console.info(`%c⏱ ${g(c,5)} /${g(d,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*f,120))}deg 100% 31%);`,n==null?void 0:n.key)}return i}}function C(e,o,n,t){return{debug:()=>{var i;return(i=e==null?void 0:e.debugAll)!=null?i:e[o]},key:!1,onChange:t}}function ze(e,o,n,t){const i=()=>{var l;return(l=r.getValue())!=null?l:e.options.renderFallbackValue},r={id:`${o.id}_${n.id}`,row:o,column:n,getValue:()=>o.getValue(t),renderValue:i,getContext:m(()=>[e,n,o,r],(l,u,s,a)=>({table:l,column:u,row:s,cell:a,getValue:a.getValue,renderValue:a.renderValue}),C(e.options,"debugCells"))};return e._features.forEach(l=>{l.createCell==null||l.createCell(r,n,o,e)},{}),r}function Le(e,o,n,t){var i,r;const u={...e._getDefaultColumnDef(),...o},s=u.accessorKey;let a=(i=(r=u.id)!=null?r:s?typeof String.prototype.replaceAll=="function"?s.replaceAll(".","_"):s.replace(/\./g,"_"):void 0)!=null?i:typeof u.header=="string"?u.header:void 0,d;if(u.accessorFn?d=u.accessorFn:s&&(s.includes(".")?d=f=>{let g=f;for(const S of s.split(".")){var p;g=(p=g)==null?void 0:p[S]}return g}:d=f=>f[u.accessorKey]),!a)throw new Error;let c={id:`${String(a)}`,accessorFn:d,parent:t,depth:n,columnDef:u,columns:[],getFlatColumns:m(()=>[!0],()=>{var f;return[c,...(f=c.columns)==null?void 0:f.flatMap(g=>g.getFlatColumns())]},C(e.options,"debugColumns")),getLeafColumns:m(()=>[e._getOrderColumnsFn()],f=>{var g;if((g=c.columns)!=null&&g.length){let p=c.columns.flatMap(S=>S.getLeafColumns());return f(p)}return[c]},C(e.options,"debugColumns"))};for(const f of e._features)f.createColumn==null||f.createColumn(c,e);return c}const $="debugHeaders";function le(e,o,n){var t;let r={id:(t=n.id)!=null?t:o.id,column:o,index:n.index,isPlaceholder:!!n.isPlaceholder,placeholderId:n.placeholderId,depth:n.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{const l=[],u=s=>{s.subHeaders&&s.subHeaders.length&&s.subHeaders.map(u),l.push(s)};return u(r),l},getContext:()=>({table:e,header:r,column:o})};return e._features.forEach(l=>{l.createHeader==null||l.createHeader(r,e)}),r}const Oe={createTable:e=>{e.getHeaderGroups=m(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(o,n,t,i)=>{var r,l;const u=(r=t==null?void 0:t.map(c=>n.find(f=>f.id===c)).filter(Boolean))!=null?r:[],s=(l=i==null?void 0:i.map(c=>n.find(f=>f.id===c)).filter(Boolean))!=null?l:[],a=n.filter(c=>!(t!=null&&t.includes(c.id))&&!(i!=null&&i.includes(c.id)));return G(o,[...u,...a,...s],e)},C(e.options,$)),e.getCenterHeaderGroups=m(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(o,n,t,i)=>(n=n.filter(r=>!(t!=null&&t.includes(r.id))&&!(i!=null&&i.includes(r.id))),G(o,n,e,"center")),C(e.options,$)),e.getLeftHeaderGroups=m(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left],(o,n,t)=>{var i;const r=(i=t==null?void 0:t.map(l=>n.find(u=>u.id===l)).filter(Boolean))!=null?i:[];return G(o,r,e,"left")},C(e.options,$)),e.getRightHeaderGroups=m(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.right],(o,n,t)=>{var i;const r=(i=t==null?void 0:t.map(l=>n.find(u=>u.id===l)).filter(Boolean))!=null?i:[];return G(o,r,e,"right")},C(e.options,$)),e.getFooterGroups=m(()=>[e.getHeaderGroups()],o=>[...o].reverse(),C(e.options,$)),e.getLeftFooterGroups=m(()=>[e.getLeftHeaderGroups()],o=>[...o].reverse(),C(e.options,$)),e.getCenterFooterGroups=m(()=>[e.getCenterHeaderGroups()],o=>[...o].reverse(),C(e.options,$)),e.getRightFooterGroups=m(()=>[e.getRightHeaderGroups()],o=>[...o].reverse(),C(e.options,$)),e.getFlatHeaders=m(()=>[e.getHeaderGroups()],o=>o.map(n=>n.headers).flat(),C(e.options,$)),e.getLeftFlatHeaders=m(()=>[e.getLeftHeaderGroups()],o=>o.map(n=>n.headers).flat(),C(e.options,$)),e.getCenterFlatHeaders=m(()=>[e.getCenterHeaderGroups()],o=>o.map(n=>n.headers).flat(),C(e.options,$)),e.getRightFlatHeaders=m(()=>[e.getRightHeaderGroups()],o=>o.map(n=>n.headers).flat(),C(e.options,$)),e.getCenterLeafHeaders=m(()=>[e.getCenterFlatHeaders()],o=>o.filter(n=>{var t;return!((t=n.subHeaders)!=null&&t.length)}),C(e.options,$)),e.getLeftLeafHeaders=m(()=>[e.getLeftFlatHeaders()],o=>o.filter(n=>{var t;return!((t=n.subHeaders)!=null&&t.length)}),C(e.options,$)),e.getRightLeafHeaders=m(()=>[e.getRightFlatHeaders()],o=>o.filter(n=>{var t;return!((t=n.subHeaders)!=null&&t.length)}),C(e.options,$)),e.getLeafHeaders=m(()=>[e.getLeftHeaderGroups(),e.getCenterHeaderGroups(),e.getRightHeaderGroups()],(o,n,t)=>{var i,r,l,u,s,a;return[...(i=(r=o[0])==null?void 0:r.headers)!=null?i:[],...(l=(u=n[0])==null?void 0:u.headers)!=null?l:[],...(s=(a=t[0])==null?void 0:a.headers)!=null?s:[]].map(d=>d.getLeafHeaders()).flat()},C(e.options,$))}};function G(e,o,n,t){var i,r;let l=0;const u=function(f,g){g===void 0&&(g=1),l=Math.max(l,g),f.filter(p=>p.getIsVisible()).forEach(p=>{var S;(S=p.columns)!=null&&S.length&&u(p.columns,g+1)},0)};u(e);let s=[];const a=(f,g)=>{const p={depth:g,id:[t,`${g}`].filter(Boolean).join("_"),headers:[]},S=[];f.forEach(w=>{const R=[...S].reverse()[0],_=w.column.depth===p.depth;let F,M=!1;if(_&&w.column.parent?F=w.column.parent:(F=w.column,M=!0),R&&(R==null?void 0:R.column)===F)R.subHeaders.push(w);else{const x=le(n,F,{id:[t,g,F.id,w==null?void 0:w.id].filter(Boolean).join("_"),isPlaceholder:M,placeholderId:M?`${S.filter(D=>D.column===F).length}`:void 0,depth:g,index:S.length});x.subHeaders.push(w),S.push(x)}p.headers.push(w),w.headerGroup=p}),s.push(p),g>0&&a(S,g-1)},d=o.map((f,g)=>le(n,f,{depth:l,index:g}));a(d,l-1),s.reverse();const c=f=>f.filter(p=>p.column.getIsVisible()).map(p=>{let S=0,w=0,R=[0];p.subHeaders&&p.subHeaders.length?(R=[],c(p.subHeaders).forEach(F=>{let{colSpan:M,rowSpan:x}=F;S+=M,R.push(x)})):S=1;const _=Math.min(...R);return w=w+_,p.colSpan=S,p.rowSpan=w,{colSpan:S,rowSpan:w}});return c((i=(r=s[0])==null?void 0:r.headers)!=null?i:[]),s}const je=(e,o,n,t,i,r,l)=>{let u={id:o,index:t,original:n,depth:i,parentId:l,_valuesCache:{},_uniqueValuesCache:{},getValue:s=>{if(u._valuesCache.hasOwnProperty(s))return u._valuesCache[s];const a=e.getColumn(s);if(a!=null&&a.accessorFn)return u._valuesCache[s]=a.accessorFn(u.original,t),u._valuesCache[s]},getUniqueValues:s=>{if(u._uniqueValuesCache.hasOwnProperty(s))return u._uniqueValuesCache[s];const a=e.getColumn(s);if(a!=null&&a.accessorFn)return a.columnDef.getUniqueValues?(u._uniqueValuesCache[s]=a.columnDef.getUniqueValues(u.original,t),u._uniqueValuesCache[s]):(u._uniqueValuesCache[s]=[u.getValue(s)],u._uniqueValuesCache[s])},renderValue:s=>{var a;return(a=u.getValue(s))!=null?a:e.options.renderFallbackValue},subRows:[],getLeafRows:()=>Ge(u.subRows,s=>s.subRows),getParentRow:()=>u.parentId?e.getRow(u.parentId,!0):void 0,getParentRows:()=>{let s=[],a=u;for(;;){const d=a.getParentRow();if(!d)break;s.push(d),a=d}return s.reverse()},getAllCells:m(()=>[e.getAllLeafColumns()],s=>s.map(a=>ze(e,u,a,a.id)),C(e.options,"debugRows")),_getAllCellsByColumnId:m(()=>[u.getAllCells()],s=>s.reduce((a,d)=>(a[d.column.id]=d,a),{}),C(e.options,"debugRows"))};for(let s=0;s<e._features.length;s++){const a=e._features[s];a==null||a.createRow==null||a.createRow(u,e)}return u},Be={createColumn:(e,o)=>{e._getFacetedRowModel=o.options.getFacetedRowModel&&o.options.getFacetedRowModel(o,e.id),e.getFacetedRowModel=()=>e._getFacetedRowModel?e._getFacetedRowModel():o.getPreFilteredRowModel(),e._getFacetedUniqueValues=o.options.getFacetedUniqueValues&&o.options.getFacetedUniqueValues(o,e.id),e.getFacetedUniqueValues=()=>e._getFacetedUniqueValues?e._getFacetedUniqueValues():new Map,e._getFacetedMinMaxValues=o.options.getFacetedMinMaxValues&&o.options.getFacetedMinMaxValues(o,e.id),e.getFacetedMinMaxValues=()=>{if(e._getFacetedMinMaxValues)return e._getFacetedMinMaxValues()}}},ae=(e,o,n)=>{var t,i;const r=n==null||(t=n.toString())==null?void 0:t.toLowerCase();return!!(!((i=e.getValue(o))==null||(i=i.toString())==null||(i=i.toLowerCase())==null)&&i.includes(r))};ae.autoRemove=e=>P(e);const ge=(e,o,n)=>{var t;return!!(!((t=e.getValue(o))==null||(t=t.toString())==null)&&t.includes(n))};ge.autoRemove=e=>P(e);const de=(e,o,n)=>{var t;return((t=e.getValue(o))==null||(t=t.toString())==null?void 0:t.toLowerCase())===(n==null?void 0:n.toLowerCase())};de.autoRemove=e=>P(e);const fe=(e,o,n)=>{var t;return(t=e.getValue(o))==null?void 0:t.includes(n)};fe.autoRemove=e=>P(e)||!(e!=null&&e.length);const ce=(e,o,n)=>!n.some(t=>{var i;return!((i=e.getValue(o))!=null&&i.includes(t))});ce.autoRemove=e=>P(e)||!(e!=null&&e.length);const pe=(e,o,n)=>n.some(t=>{var i;return(i=e.getValue(o))==null?void 0:i.includes(t)});pe.autoRemove=e=>P(e)||!(e!=null&&e.length);const Se=(e,o,n)=>e.getValue(o)===n;Se.autoRemove=e=>P(e);const me=(e,o,n)=>e.getValue(o)==n;me.autoRemove=e=>P(e);const b=(e,o,n)=>{let[t,i]=n;const r=e.getValue(o);return r>=t&&r<=i};b.resolveFilterValue=e=>{let[o,n]=e,t=typeof o!="number"?parseFloat(o):o,i=typeof n!="number"?parseFloat(n):n,r=o===null||Number.isNaN(t)?-1/0:t,l=n===null||Number.isNaN(i)?1/0:i;if(r>l){const u=r;r=l,l=u}return[r,l]};b.autoRemove=e=>P(e)||P(e[0])&&P(e[1]);const V={includesString:ae,includesStringSensitive:ge,equalsString:de,arrIncludes:fe,arrIncludesAll:ce,arrIncludesSome:pe,equals:Se,weakEquals:me,inNumberRange:b};function P(e){return e==null||e===""}const Te={getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:e=>({columnFilters:[],...e}),getDefaultOptions:e=>({onColumnFiltersChange:v("columnFilters",e),filterFromLeafRows:!1,maxLeafRowFilterDepth:100}),createColumn:(e,o)=>{e.getAutoFilterFn=()=>{const n=o.getCoreRowModel().flatRows[0],t=n==null?void 0:n.getValue(e.id);return typeof t=="string"?V.includesString:typeof t=="number"?V.inNumberRange:typeof t=="boolean"||t!==null&&typeof t=="object"?V.equals:Array.isArray(t)?V.arrIncludes:V.weakEquals},e.getFilterFn=()=>{var n,t;return O(e.columnDef.filterFn)?e.columnDef.filterFn:e.columnDef.filterFn==="auto"?e.getAutoFilterFn():(n=(t=o.options.filterFns)==null?void 0:t[e.columnDef.filterFn])!=null?n:V[e.columnDef.filterFn]},e.getCanFilter=()=>{var n,t,i;return((n=e.columnDef.enableColumnFilter)!=null?n:!0)&&((t=o.options.enableColumnFilters)!=null?t:!0)&&((i=o.options.enableFilters)!=null?i:!0)&&!!e.accessorFn},e.getIsFiltered=()=>e.getFilterIndex()>-1,e.getFilterValue=()=>{var n;return(n=o.getState().columnFilters)==null||(n=n.find(t=>t.id===e.id))==null?void 0:n.value},e.getFilterIndex=()=>{var n,t;return(n=(t=o.getState().columnFilters)==null?void 0:t.findIndex(i=>i.id===e.id))!=null?n:-1},e.setFilterValue=n=>{o.setColumnFilters(t=>{const i=e.getFilterFn(),r=t==null?void 0:t.find(d=>d.id===e.id),l=I(n,r?r.value:void 0);if(ue(i,l,e)){var u;return(u=t==null?void 0:t.filter(d=>d.id!==e.id))!=null?u:[]}const s={id:e.id,value:l};if(r){var a;return(a=t==null?void 0:t.map(d=>d.id===e.id?s:d))!=null?a:[]}return t!=null&&t.length?[...t,s]:[s]})}},createRow:(e,o)=>{e.columnFilters={},e.columnFiltersMeta={}},createTable:e=>{e.setColumnFilters=o=>{const n=e.getAllLeafColumns(),t=i=>{var r;return(r=I(o,i))==null?void 0:r.filter(l=>{const u=n.find(s=>s.id===l.id);if(u){const s=u.getFilterFn();if(ue(s,l.value,u))return!1}return!0})};e.options.onColumnFiltersChange==null||e.options.onColumnFiltersChange(t)},e.resetColumnFilters=o=>{var n,t;e.setColumnFilters(o?[]:(n=(t=e.initialState)==null?void 0:t.columnFilters)!=null?n:[])},e.getPreFilteredRowModel=()=>e.getCoreRowModel(),e.getFilteredRowModel=()=>(!e._getFilteredRowModel&&e.options.getFilteredRowModel&&(e._getFilteredRowModel=e.options.getFilteredRowModel(e)),e.options.manualFiltering||!e._getFilteredRowModel?e.getPreFilteredRowModel():e._getFilteredRowModel())}};function ue(e,o,n){return(e&&e.autoRemove?e.autoRemove(o,n):!1)||typeof o>"u"||typeof o=="string"&&!o}const Ne=(e,o,n)=>n.reduce((t,i)=>{const r=i.getValue(e);return t+(typeof r=="number"?r:0)},0),ke=(e,o,n)=>{let t;return n.forEach(i=>{const r=i.getValue(e);r!=null&&(t>r||t===void 0&&r>=r)&&(t=r)}),t},qe=(e,o,n)=>{let t;return n.forEach(i=>{const r=i.getValue(e);r!=null&&(t<r||t===void 0&&r>=r)&&(t=r)}),t},Ue=(e,o,n)=>{let t,i;return n.forEach(r=>{const l=r.getValue(e);l!=null&&(t===void 0?l>=l&&(t=i=l):(t>l&&(t=l),i<l&&(i=l)))}),[t,i]},Xe=(e,o)=>{let n=0,t=0;if(o.forEach(i=>{let r=i.getValue(e);r!=null&&(r=+r)>=r&&(++n,t+=r)}),n)return t/n},Ke=(e,o)=>{if(!o.length)return;const n=o.map(r=>r.getValue(e));if(!Ae(n))return;if(n.length===1)return n[0];const t=Math.floor(n.length/2),i=n.sort((r,l)=>r-l);return n.length%2!==0?i[t]:(i[t-1]+i[t])/2},Je=(e,o)=>Array.from(new Set(o.map(n=>n.getValue(e))).values()),Qe=(e,o)=>new Set(o.map(n=>n.getValue(e))).size,We=(e,o)=>o.length,T={sum:Ne,min:ke,max:qe,extent:Ue,mean:Xe,median:Ke,unique:Je,uniqueCount:Qe,count:We},Ye={getDefaultColumnDef:()=>({aggregatedCell:e=>{var o,n;return(o=(n=e.getValue())==null||n.toString==null?void 0:n.toString())!=null?o:null},aggregationFn:"auto"}),getInitialState:e=>({grouping:[],...e}),getDefaultOptions:e=>({onGroupingChange:v("grouping",e),groupedColumnMode:"reorder"}),createColumn:(e,o)=>{e.toggleGrouping=()=>{o.setGrouping(n=>n!=null&&n.includes(e.id)?n.filter(t=>t!==e.id):[...n??[],e.id])},e.getCanGroup=()=>{var n,t;return((n=e.columnDef.enableGrouping)!=null?n:!0)&&((t=o.options.enableGrouping)!=null?t:!0)&&(!!e.accessorFn||!!e.columnDef.getGroupingValue)},e.getIsGrouped=()=>{var n;return(n=o.getState().grouping)==null?void 0:n.includes(e.id)},e.getGroupedIndex=()=>{var n;return(n=o.getState().grouping)==null?void 0:n.indexOf(e.id)},e.getToggleGroupingHandler=()=>{const n=e.getCanGroup();return()=>{n&&e.toggleGrouping()}},e.getAutoAggregationFn=()=>{const n=o.getCoreRowModel().flatRows[0],t=n==null?void 0:n.getValue(e.id);if(typeof t=="number")return T.sum;if(Object.prototype.toString.call(t)==="[object Date]")return T.extent},e.getAggregationFn=()=>{var n,t;if(!e)throw new Error;return O(e.columnDef.aggregationFn)?e.columnDef.aggregationFn:e.columnDef.aggregationFn==="auto"?e.getAutoAggregationFn():(n=(t=o.options.aggregationFns)==null?void 0:t[e.columnDef.aggregationFn])!=null?n:T[e.columnDef.aggregationFn]}},createTable:e=>{e.setGrouping=o=>e.options.onGroupingChange==null?void 0:e.options.onGroupingChange(o),e.resetGrouping=o=>{var n,t;e.setGrouping(o?[]:(n=(t=e.initialState)==null?void 0:t.grouping)!=null?n:[])},e.getPreGroupedRowModel=()=>e.getFilteredRowModel(),e.getGroupedRowModel=()=>(!e._getGroupedRowModel&&e.options.getGroupedRowModel&&(e._getGroupedRowModel=e.options.getGroupedRowModel(e)),e.options.manualGrouping||!e._getGroupedRowModel?e.getPreGroupedRowModel():e._getGroupedRowModel())},createRow:(e,o)=>{e.getIsGrouped=()=>!!e.groupingColumnId,e.getGroupingValue=n=>{if(e._groupingValuesCache.hasOwnProperty(n))return e._groupingValuesCache[n];const t=o.getColumn(n);return t!=null&&t.columnDef.getGroupingValue?(e._groupingValuesCache[n]=t.columnDef.getGroupingValue(e.original),e._groupingValuesCache[n]):e.getValue(n)},e._groupingValuesCache={}},createCell:(e,o,n,t)=>{e.getIsGrouped=()=>o.getIsGrouped()&&o.id===n.groupingColumnId,e.getIsPlaceholder=()=>!e.getIsGrouped()&&o.getIsGrouped(),e.getIsAggregated=()=>{var i;return!e.getIsGrouped()&&!e.getIsPlaceholder()&&!!((i=n.subRows)!=null&&i.length)}}};function Ze(e,o,n){if(!(o!=null&&o.length)||!n)return e;const t=e.filter(r=>!o.includes(r.id));return n==="remove"?t:[...o.map(r=>e.find(l=>l.id===r)).filter(Boolean),...t]}const be={getInitialState:e=>({columnOrder:[],...e}),getDefaultOptions:e=>({onColumnOrderChange:v("columnOrder",e)}),createColumn:(e,o)=>{e.getIndex=m(n=>[A(o,n)],n=>n.findIndex(t=>t.id===e.id),C(o.options,"debugColumns")),e.getIsFirstColumn=n=>{var t;return((t=A(o,n)[0])==null?void 0:t.id)===e.id},e.getIsLastColumn=n=>{var t;const i=A(o,n);return((t=i[i.length-1])==null?void 0:t.id)===e.id}},createTable:e=>{e.setColumnOrder=o=>e.options.onColumnOrderChange==null?void 0:e.options.onColumnOrderChange(o),e.resetColumnOrder=o=>{var n;e.setColumnOrder(o?[]:(n=e.initialState.columnOrder)!=null?n:[])},e._getOrderColumnsFn=m(()=>[e.getState().columnOrder,e.getState().grouping,e.options.groupedColumnMode],(o,n,t)=>i=>{let r=[];if(!(o!=null&&o.length))r=i;else{const l=[...o],u=[...i];for(;u.length&&l.length;){const s=l.shift(),a=u.findIndex(d=>d.id===s);a>-1&&r.push(u.splice(a,1)[0])}r=[...r,...u]}return Ze(r,n,t)},C(e.options,"debugTable"))}},N=()=>({left:[],right:[]}),en={getInitialState:e=>({columnPinning:N(),...e}),getDefaultOptions:e=>({onColumnPinningChange:v("columnPinning",e)}),createColumn:(e,o)=>{e.pin=n=>{const t=e.getLeafColumns().map(i=>i.id).filter(Boolean);o.setColumnPinning(i=>{var r,l;if(n==="right"){var u,s;return{left:((u=i==null?void 0:i.left)!=null?u:[]).filter(c=>!(t!=null&&t.includes(c))),right:[...((s=i==null?void 0:i.right)!=null?s:[]).filter(c=>!(t!=null&&t.includes(c))),...t]}}if(n==="left"){var a,d;return{left:[...((a=i==null?void 0:i.left)!=null?a:[]).filter(c=>!(t!=null&&t.includes(c))),...t],right:((d=i==null?void 0:i.right)!=null?d:[]).filter(c=>!(t!=null&&t.includes(c)))}}return{left:((r=i==null?void 0:i.left)!=null?r:[]).filter(c=>!(t!=null&&t.includes(c))),right:((l=i==null?void 0:i.right)!=null?l:[]).filter(c=>!(t!=null&&t.includes(c)))}})},e.getCanPin=()=>e.getLeafColumns().some(t=>{var i,r,l;return((i=t.columnDef.enablePinning)!=null?i:!0)&&((r=(l=o.options.enableColumnPinning)!=null?l:o.options.enablePinning)!=null?r:!0)}),e.getIsPinned=()=>{const n=e.getLeafColumns().map(u=>u.id),{left:t,right:i}=o.getState().columnPinning,r=n.some(u=>t==null?void 0:t.includes(u)),l=n.some(u=>i==null?void 0:i.includes(u));return r?"left":l?"right":!1},e.getPinnedIndex=()=>{var n,t;const i=e.getIsPinned();return i?(n=(t=o.getState().columnPinning)==null||(t=t[i])==null?void 0:t.indexOf(e.id))!=null?n:-1:0}},createRow:(e,o)=>{e.getCenterVisibleCells=m(()=>[e._getAllVisibleCells(),o.getState().columnPinning.left,o.getState().columnPinning.right],(n,t,i)=>{const r=[...t??[],...i??[]];return n.filter(l=>!r.includes(l.column.id))},C(o.options,"debugRows")),e.getLeftVisibleCells=m(()=>[e._getAllVisibleCells(),o.getState().columnPinning.left],(n,t)=>(t??[]).map(r=>n.find(l=>l.column.id===r)).filter(Boolean).map(r=>({...r,position:"left"})),C(o.options,"debugRows")),e.getRightVisibleCells=m(()=>[e._getAllVisibleCells(),o.getState().columnPinning.right],(n,t)=>(t??[]).map(r=>n.find(l=>l.column.id===r)).filter(Boolean).map(r=>({...r,position:"right"})),C(o.options,"debugRows"))},createTable:e=>{e.setColumnPinning=o=>e.options.onColumnPinningChange==null?void 0:e.options.onColumnPinningChange(o),e.resetColumnPinning=o=>{var n,t;return e.setColumnPinning(o?N():(n=(t=e.initialState)==null?void 0:t.columnPinning)!=null?n:N())},e.getIsSomeColumnsPinned=o=>{var n;const t=e.getState().columnPinning;if(!o){var i,r;return!!((i=t.left)!=null&&i.length||(r=t.right)!=null&&r.length)}return!!((n=t[o])!=null&&n.length)},e.getLeftLeafColumns=m(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left],(o,n)=>(n??[]).map(t=>o.find(i=>i.id===t)).filter(Boolean),C(e.options,"debugColumns")),e.getRightLeafColumns=m(()=>[e.getAllLeafColumns(),e.getState().columnPinning.right],(o,n)=>(n??[]).map(t=>o.find(i=>i.id===t)).filter(Boolean),C(e.options,"debugColumns")),e.getCenterLeafColumns=m(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(o,n,t)=>{const i=[...n??[],...t??[]];return o.filter(r=>!i.includes(r.id))},C(e.options,"debugColumns"))}},z={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},k=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),nn={getDefaultColumnDef:()=>z,getInitialState:e=>({columnSizing:{},columnSizingInfo:k(),...e}),getDefaultOptions:e=>({columnResizeMode:"onEnd",columnResizeDirection:"ltr",onColumnSizingChange:v("columnSizing",e),onColumnSizingInfoChange:v("columnSizingInfo",e)}),createColumn:(e,o)=>{e.getSize=()=>{var n,t,i;const r=o.getState().columnSizing[e.id];return Math.min(Math.max((n=e.columnDef.minSize)!=null?n:z.minSize,(t=r??e.columnDef.size)!=null?t:z.size),(i=e.columnDef.maxSize)!=null?i:z.maxSize)},e.getStart=m(n=>[n,A(o,n),o.getState().columnSizing],(n,t)=>t.slice(0,e.getIndex(n)).reduce((i,r)=>i+r.getSize(),0),C(o.options,"debugColumns")),e.getAfter=m(n=>[n,A(o,n),o.getState().columnSizing],(n,t)=>t.slice(e.getIndex(n)+1).reduce((i,r)=>i+r.getSize(),0),C(o.options,"debugColumns")),e.resetSize=()=>{o.setColumnSizing(n=>{let{[e.id]:t,...i}=n;return i})},e.getCanResize=()=>{var n,t;return((n=e.columnDef.enableResizing)!=null?n:!0)&&((t=o.options.enableColumnResizing)!=null?t:!0)},e.getIsResizing=()=>o.getState().columnSizingInfo.isResizingColumn===e.id},createHeader:(e,o)=>{e.getSize=()=>{let n=0;const t=i=>{if(i.subHeaders.length)i.subHeaders.forEach(t);else{var r;n+=(r=i.column.getSize())!=null?r:0}};return t(e),n},e.getStart=()=>{if(e.index>0){const n=e.headerGroup.headers[e.index-1];return n.getStart()+n.getSize()}return 0},e.getResizeHandler=n=>{const t=o.getColumn(e.column.id),i=t==null?void 0:t.getCanResize();return r=>{if(!t||!i||(r.persist==null||r.persist(),q(r)&&r.touches&&r.touches.length>1))return;const l=e.getSize(),u=e?e.getLeafHeaders().map(R=>[R.column.id,R.column.getSize()]):[[t.id,t.getSize()]],s=q(r)?Math.round(r.touches[0].clientX):r.clientX,a={},d=(R,_)=>{typeof _=="number"&&(o.setColumnSizingInfo(F=>{var M,x;const D=o.options.columnResizeDirection==="rtl"?-1:1,te=(_-((M=F==null?void 0:F.startOffset)!=null?M:0))*D,oe=Math.max(te/((x=F==null?void 0:F.startSize)!=null?x:0),-.999999);return F.columnSizingStart.forEach(Re=>{let[we,ie]=Re;a[we]=Math.round(Math.max(ie+ie*oe,0)*100)/100}),{...F,deltaOffset:te,deltaPercentage:oe}}),(o.options.columnResizeMode==="onChange"||R==="end")&&o.setColumnSizing(F=>({...F,...a})))},c=R=>d("move",R),f=R=>{d("end",R),o.setColumnSizingInfo(_=>({..._,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},g=n||typeof document<"u"?document:null,p={moveHandler:R=>c(R.clientX),upHandler:R=>{g==null||g.removeEventListener("mousemove",p.moveHandler),g==null||g.removeEventListener("mouseup",p.upHandler),f(R.clientX)}},S={moveHandler:R=>(R.cancelable&&(R.preventDefault(),R.stopPropagation()),c(R.touches[0].clientX),!1),upHandler:R=>{var _;g==null||g.removeEventListener("touchmove",S.moveHandler),g==null||g.removeEventListener("touchend",S.upHandler),R.cancelable&&(R.preventDefault(),R.stopPropagation()),f((_=R.touches[0])==null?void 0:_.clientX)}},w=tn()?{passive:!1}:!1;q(r)?(g==null||g.addEventListener("touchmove",S.moveHandler,w),g==null||g.addEventListener("touchend",S.upHandler,w)):(g==null||g.addEventListener("mousemove",p.moveHandler,w),g==null||g.addEventListener("mouseup",p.upHandler,w)),o.setColumnSizingInfo(R=>({...R,startOffset:s,startSize:l,deltaOffset:0,deltaPercentage:0,columnSizingStart:u,isResizingColumn:t.id}))}}},createTable:e=>{e.setColumnSizing=o=>e.options.onColumnSizingChange==null?void 0:e.options.onColumnSizingChange(o),e.setColumnSizingInfo=o=>e.options.onColumnSizingInfoChange==null?void 0:e.options.onColumnSizingInfoChange(o),e.resetColumnSizing=o=>{var n;e.setColumnSizing(o?{}:(n=e.initialState.columnSizing)!=null?n:{})},e.resetHeaderSizeInfo=o=>{var n;e.setColumnSizingInfo(o?k():(n=e.initialState.columnSizingInfo)!=null?n:k())},e.getTotalSize=()=>{var o,n;return(o=(n=e.getHeaderGroups()[0])==null?void 0:n.headers.reduce((t,i)=>t+i.getSize(),0))!=null?o:0},e.getLeftTotalSize=()=>{var o,n;return(o=(n=e.getLeftHeaderGroups()[0])==null?void 0:n.headers.reduce((t,i)=>t+i.getSize(),0))!=null?o:0},e.getCenterTotalSize=()=>{var o,n;return(o=(n=e.getCenterHeaderGroups()[0])==null?void 0:n.headers.reduce((t,i)=>t+i.getSize(),0))!=null?o:0},e.getRightTotalSize=()=>{var o,n;return(o=(n=e.getRightHeaderGroups()[0])==null?void 0:n.headers.reduce((t,i)=>t+i.getSize(),0))!=null?o:0}}};let L=null;function tn(){if(typeof L=="boolean")return L;let e=!1;try{const o={get passive(){return e=!0,!1}},n=()=>{};window.addEventListener("test",n,o),window.removeEventListener("test",n)}catch{e=!1}return L=e,L}function q(e){return e.type==="touchstart"}const on={getInitialState:e=>({columnVisibility:{},...e}),getDefaultOptions:e=>({onColumnVisibilityChange:v("columnVisibility",e)}),createColumn:(e,o)=>{e.toggleVisibility=n=>{e.getCanHide()&&o.setColumnVisibility(t=>({...t,[e.id]:n??!e.getIsVisible()}))},e.getIsVisible=()=>{var n,t;const i=e.columns;return(n=i.length?i.some(r=>r.getIsVisible()):(t=o.getState().columnVisibility)==null?void 0:t[e.id])!=null?n:!0},e.getCanHide=()=>{var n,t;return((n=e.columnDef.enableHiding)!=null?n:!0)&&((t=o.options.enableHiding)!=null?t:!0)},e.getToggleVisibilityHandler=()=>n=>{e.toggleVisibility==null||e.toggleVisibility(n.target.checked)}},createRow:(e,o)=>{e._getAllVisibleCells=m(()=>[e.getAllCells(),o.getState().columnVisibility],n=>n.filter(t=>t.column.getIsVisible()),C(o.options,"debugRows")),e.getVisibleCells=m(()=>[e.getLeftVisibleCells(),e.getCenterVisibleCells(),e.getRightVisibleCells()],(n,t,i)=>[...n,...t,...i],C(o.options,"debugRows"))},createTable:e=>{const o=(n,t)=>m(()=>[t(),t().filter(i=>i.getIsVisible()).map(i=>i.id).join("_")],i=>i.filter(r=>r.getIsVisible==null?void 0:r.getIsVisible()),C(e.options,"debugColumns"));e.getVisibleFlatColumns=o("getVisibleFlatColumns",()=>e.getAllFlatColumns()),e.getVisibleLeafColumns=o("getVisibleLeafColumns",()=>e.getAllLeafColumns()),e.getLeftVisibleLeafColumns=o("getLeftVisibleLeafColumns",()=>e.getLeftLeafColumns()),e.getRightVisibleLeafColumns=o("getRightVisibleLeafColumns",()=>e.getRightLeafColumns()),e.getCenterVisibleLeafColumns=o("getCenterVisibleLeafColumns",()=>e.getCenterLeafColumns()),e.setColumnVisibility=n=>e.options.onColumnVisibilityChange==null?void 0:e.options.onColumnVisibilityChange(n),e.resetColumnVisibility=n=>{var t;e.setColumnVisibility(n?{}:(t=e.initialState.columnVisibility)!=null?t:{})},e.toggleAllColumnsVisible=n=>{var t;n=(t=n)!=null?t:!e.getIsAllColumnsVisible(),e.setColumnVisibility(e.getAllLeafColumns().reduce((i,r)=>({...i,[r.id]:n||!(r.getCanHide!=null&&r.getCanHide())}),{}))},e.getIsAllColumnsVisible=()=>!e.getAllLeafColumns().some(n=>!(n.getIsVisible!=null&&n.getIsVisible())),e.getIsSomeColumnsVisible=()=>e.getAllLeafColumns().some(n=>n.getIsVisible==null?void 0:n.getIsVisible()),e.getToggleAllColumnsVisibilityHandler=()=>n=>{var t;e.toggleAllColumnsVisible((t=n.target)==null?void 0:t.checked)}}};function A(e,o){return o?o==="center"?e.getCenterVisibleLeafColumns():o==="left"?e.getLeftVisibleLeafColumns():e.getRightVisibleLeafColumns():e.getVisibleLeafColumns()}const rn={createTable:e=>{e._getGlobalFacetedRowModel=e.options.getFacetedRowModel&&e.options.getFacetedRowModel(e,"__global__"),e.getGlobalFacetedRowModel=()=>e.options.manualFiltering||!e._getGlobalFacetedRowModel?e.getPreFilteredRowModel():e._getGlobalFacetedRowModel(),e._getGlobalFacetedUniqueValues=e.options.getFacetedUniqueValues&&e.options.getFacetedUniqueValues(e,"__global__"),e.getGlobalFacetedUniqueValues=()=>e._getGlobalFacetedUniqueValues?e._getGlobalFacetedUniqueValues():new Map,e._getGlobalFacetedMinMaxValues=e.options.getFacetedMinMaxValues&&e.options.getFacetedMinMaxValues(e,"__global__"),e.getGlobalFacetedMinMaxValues=()=>{if(e._getGlobalFacetedMinMaxValues)return e._getGlobalFacetedMinMaxValues()}}},ln={getInitialState:e=>({globalFilter:void 0,...e}),getDefaultOptions:e=>({onGlobalFilterChange:v("globalFilter",e),globalFilterFn:"auto",getColumnCanGlobalFilter:o=>{var n;const t=(n=e.getCoreRowModel().flatRows[0])==null||(n=n._getAllCellsByColumnId()[o.id])==null?void 0:n.getValue();return typeof t=="string"||typeof t=="number"}}),createColumn:(e,o)=>{e.getCanGlobalFilter=()=>{var n,t,i,r;return((n=e.columnDef.enableGlobalFilter)!=null?n:!0)&&((t=o.options.enableGlobalFilter)!=null?t:!0)&&((i=o.options.enableFilters)!=null?i:!0)&&((r=o.options.getColumnCanGlobalFilter==null?void 0:o.options.getColumnCanGlobalFilter(e))!=null?r:!0)&&!!e.accessorFn}},createTable:e=>{e.getGlobalAutoFilterFn=()=>V.includesString,e.getGlobalFilterFn=()=>{var o,n;const{globalFilterFn:t}=e.options;return O(t)?t:t==="auto"?e.getGlobalAutoFilterFn():(o=(n=e.options.filterFns)==null?void 0:n[t])!=null?o:V[t]},e.setGlobalFilter=o=>{e.options.onGlobalFilterChange==null||e.options.onGlobalFilterChange(o)},e.resetGlobalFilter=o=>{e.setGlobalFilter(o?void 0:e.initialState.globalFilter)}}},un={getInitialState:e=>({expanded:{},...e}),getDefaultOptions:e=>({onExpandedChange:v("expanded",e),paginateExpandedRows:!0}),createTable:e=>{let o=!1,n=!1;e._autoResetExpanded=()=>{var t,i;if(!o){e._queue(()=>{o=!0});return}if((t=(i=e.options.autoResetAll)!=null?i:e.options.autoResetExpanded)!=null?t:!e.options.manualExpanding){if(n)return;n=!0,e._queue(()=>{e.resetExpanded(),n=!1})}},e.setExpanded=t=>e.options.onExpandedChange==null?void 0:e.options.onExpandedChange(t),e.toggleAllRowsExpanded=t=>{t??!e.getIsAllRowsExpanded()?e.setExpanded(!0):e.setExpanded({})},e.resetExpanded=t=>{var i,r;e.setExpanded(t?{}:(i=(r=e.initialState)==null?void 0:r.expanded)!=null?i:{})},e.getCanSomeRowsExpand=()=>e.getPrePaginationRowModel().flatRows.some(t=>t.getCanExpand()),e.getToggleAllRowsExpandedHandler=()=>t=>{t.persist==null||t.persist(),e.toggleAllRowsExpanded()},e.getIsSomeRowsExpanded=()=>{const t=e.getState().expanded;return t===!0||Object.values(t).some(Boolean)},e.getIsAllRowsExpanded=()=>{const t=e.getState().expanded;return typeof t=="boolean"?t===!0:!(!Object.keys(t).length||e.getRowModel().flatRows.some(i=>!i.getIsExpanded()))},e.getExpandedDepth=()=>{let t=0;return(e.getState().expanded===!0?Object.keys(e.getRowModel().rowsById):Object.keys(e.getState().expanded)).forEach(r=>{const l=r.split(".");t=Math.max(t,l.length)}),t},e.getPreExpandedRowModel=()=>e.getSortedRowModel(),e.getExpandedRowModel=()=>(!e._getExpandedRowModel&&e.options.getExpandedRowModel&&(e._getExpandedRowModel=e.options.getExpandedRowModel(e)),e.options.manualExpanding||!e._getExpandedRowModel?e.getPreExpandedRowModel():e._getExpandedRowModel())},createRow:(e,o)=>{e.toggleExpanded=n=>{o.setExpanded(t=>{var i;const r=t===!0?!0:!!(t!=null&&t[e.id]);let l={};if(t===!0?Object.keys(o.getRowModel().rowsById).forEach(u=>{l[u]=!0}):l=t,n=(i=n)!=null?i:!r,!r&&n)return{...l,[e.id]:!0};if(r&&!n){const{[e.id]:u,...s}=l;return s}return t})},e.getIsExpanded=()=>{var n;const t=o.getState().expanded;return!!((n=o.options.getIsRowExpanded==null?void 0:o.options.getIsRowExpanded(e))!=null?n:t===!0||t!=null&&t[e.id])},e.getCanExpand=()=>{var n,t,i;return(n=o.options.getRowCanExpand==null?void 0:o.options.getRowCanExpand(e))!=null?n:((t=o.options.enableExpanding)!=null?t:!0)&&!!((i=e.subRows)!=null&&i.length)},e.getIsAllParentsExpanded=()=>{let n=!0,t=e;for(;n&&t.parentId;)t=o.getRow(t.parentId,!0),n=t.getIsExpanded();return n},e.getToggleExpandedHandler=()=>{const n=e.getCanExpand();return()=>{n&&e.toggleExpanded()}}}},J=0,Q=10,U=()=>({pageIndex:J,pageSize:Q}),sn={getInitialState:e=>({...e,pagination:{...U(),...e==null?void 0:e.pagination}}),getDefaultOptions:e=>({onPaginationChange:v("pagination",e)}),createTable:e=>{let o=!1,n=!1;e._autoResetPageIndex=()=>{var t,i;if(!o){e._queue(()=>{o=!0});return}if((t=(i=e.options.autoResetAll)!=null?i:e.options.autoResetPageIndex)!=null?t:!e.options.manualPagination){if(n)return;n=!0,e._queue(()=>{e.resetPageIndex(),n=!1})}},e.setPagination=t=>{const i=r=>I(t,r);return e.options.onPaginationChange==null?void 0:e.options.onPaginationChange(i)},e.resetPagination=t=>{var i;e.setPagination(t?U():(i=e.initialState.pagination)!=null?i:U())},e.setPageIndex=t=>{e.setPagination(i=>{let r=I(t,i.pageIndex);const l=typeof e.options.pageCount>"u"||e.options.pageCount===-1?Number.MAX_SAFE_INTEGER:e.options.pageCount-1;return r=Math.max(0,Math.min(r,l)),{...i,pageIndex:r}})},e.resetPageIndex=t=>{var i,r;e.setPageIndex(t?J:(i=(r=e.initialState)==null||(r=r.pagination)==null?void 0:r.pageIndex)!=null?i:J)},e.resetPageSize=t=>{var i,r;e.setPageSize(t?Q:(i=(r=e.initialState)==null||(r=r.pagination)==null?void 0:r.pageSize)!=null?i:Q)},e.setPageSize=t=>{e.setPagination(i=>{const r=Math.max(1,I(t,i.pageSize)),l=i.pageSize*i.pageIndex,u=Math.floor(l/r);return{...i,pageIndex:u,pageSize:r}})},e.setPageCount=t=>e.setPagination(i=>{var r;let l=I(t,(r=e.options.pageCount)!=null?r:-1);return typeof l=="number"&&(l=Math.max(-1,l)),{...i,pageCount:l}}),e.getPageOptions=m(()=>[e.getPageCount()],t=>{let i=[];return t&&t>0&&(i=[...new Array(t)].fill(null).map((r,l)=>l)),i},C(e.options,"debugTable")),e.getCanPreviousPage=()=>e.getState().pagination.pageIndex>0,e.getCanNextPage=()=>{const{pageIndex:t}=e.getState().pagination,i=e.getPageCount();return i===-1?!0:i===0?!1:t<i-1},e.previousPage=()=>e.setPageIndex(t=>t-1),e.nextPage=()=>e.setPageIndex(t=>t+1),e.firstPage=()=>e.setPageIndex(0),e.lastPage=()=>e.setPageIndex(e.getPageCount()-1),e.getPrePaginationRowModel=()=>e.getExpandedRowModel(),e.getPaginationRowModel=()=>(!e._getPaginationRowModel&&e.options.getPaginationRowModel&&(e._getPaginationRowModel=e.options.getPaginationRowModel(e)),e.options.manualPagination||!e._getPaginationRowModel?e.getPrePaginationRowModel():e._getPaginationRowModel()),e.getPageCount=()=>{var t;return(t=e.options.pageCount)!=null?t:Math.ceil(e.getRowCount()/e.getState().pagination.pageSize)},e.getRowCount=()=>{var t;return(t=e.options.rowCount)!=null?t:e.getPrePaginationRowModel().rows.length}}},X=()=>({top:[],bottom:[]}),an={getInitialState:e=>({rowPinning:X(),...e}),getDefaultOptions:e=>({onRowPinningChange:v("rowPinning",e)}),createRow:(e,o)=>{e.pin=(n,t,i)=>{const r=t?e.getLeafRows().map(s=>{let{id:a}=s;return a}):[],l=i?e.getParentRows().map(s=>{let{id:a}=s;return a}):[],u=new Set([...l,e.id,...r]);o.setRowPinning(s=>{var a,d;if(n==="bottom"){var c,f;return{top:((c=s==null?void 0:s.top)!=null?c:[]).filter(S=>!(u!=null&&u.has(S))),bottom:[...((f=s==null?void 0:s.bottom)!=null?f:[]).filter(S=>!(u!=null&&u.has(S))),...Array.from(u)]}}if(n==="top"){var g,p;return{top:[...((g=s==null?void 0:s.top)!=null?g:[]).filter(S=>!(u!=null&&u.has(S))),...Array.from(u)],bottom:((p=s==null?void 0:s.bottom)!=null?p:[]).filter(S=>!(u!=null&&u.has(S)))}}return{top:((a=s==null?void 0:s.top)!=null?a:[]).filter(S=>!(u!=null&&u.has(S))),bottom:((d=s==null?void 0:s.bottom)!=null?d:[]).filter(S=>!(u!=null&&u.has(S)))}})},e.getCanPin=()=>{var n;const{enableRowPinning:t,enablePinning:i}=o.options;return typeof t=="function"?t(e):(n=t??i)!=null?n:!0},e.getIsPinned=()=>{const n=[e.id],{top:t,bottom:i}=o.getState().rowPinning,r=n.some(u=>t==null?void 0:t.includes(u)),l=n.some(u=>i==null?void 0:i.includes(u));return r?"top":l?"bottom":!1},e.getPinnedIndex=()=>{var n,t;const i=e.getIsPinned();if(!i)return-1;const r=(n=i==="top"?o.getTopRows():o.getBottomRows())==null?void 0:n.map(l=>{let{id:u}=l;return u});return(t=r==null?void 0:r.indexOf(e.id))!=null?t:-1}},createTable:e=>{e.setRowPinning=o=>e.options.onRowPinningChange==null?void 0:e.options.onRowPinningChange(o),e.resetRowPinning=o=>{var n,t;return e.setRowPinning(o?X():(n=(t=e.initialState)==null?void 0:t.rowPinning)!=null?n:X())},e.getIsSomeRowsPinned=o=>{var n;const t=e.getState().rowPinning;if(!o){var i,r;return!!((i=t.top)!=null&&i.length||(r=t.bottom)!=null&&r.length)}return!!((n=t[o])!=null&&n.length)},e._getPinnedRows=(o,n,t)=>{var i;return((i=e.options.keepPinnedRows)==null||i?(n??[]).map(l=>{const u=e.getRow(l,!0);return u.getIsAllParentsExpanded()?u:null}):(n??[]).map(l=>o.find(u=>u.id===l))).filter(Boolean).map(l=>({...l,position:t}))},e.getTopRows=m(()=>[e.getRowModel().rows,e.getState().rowPinning.top],(o,n)=>e._getPinnedRows(o,n,"top"),C(e.options,"debugRows")),e.getBottomRows=m(()=>[e.getRowModel().rows,e.getState().rowPinning.bottom],(o,n)=>e._getPinnedRows(o,n,"bottom"),C(e.options,"debugRows")),e.getCenterRows=m(()=>[e.getRowModel().rows,e.getState().rowPinning.top,e.getState().rowPinning.bottom],(o,n,t)=>{const i=new Set([...n??[],...t??[]]);return o.filter(r=>!i.has(r.id))},C(e.options,"debugRows"))}},gn={getInitialState:e=>({rowSelection:{},...e}),getDefaultOptions:e=>({onRowSelectionChange:v("rowSelection",e),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:e=>{e.setRowSelection=o=>e.options.onRowSelectionChange==null?void 0:e.options.onRowSelectionChange(o),e.resetRowSelection=o=>{var n;return e.setRowSelection(o?{}:(n=e.initialState.rowSelection)!=null?n:{})},e.toggleAllRowsSelected=o=>{e.setRowSelection(n=>{o=typeof o<"u"?o:!e.getIsAllRowsSelected();const t={...n},i=e.getPreGroupedRowModel().flatRows;return o?i.forEach(r=>{r.getCanSelect()&&(t[r.id]=!0)}):i.forEach(r=>{delete t[r.id]}),t})},e.toggleAllPageRowsSelected=o=>e.setRowSelection(n=>{const t=typeof o<"u"?o:!e.getIsAllPageRowsSelected(),i={...n};return e.getRowModel().rows.forEach(r=>{W(i,r.id,t,!0,e)}),i}),e.getPreSelectedRowModel=()=>e.getCoreRowModel(),e.getSelectedRowModel=m(()=>[e.getState().rowSelection,e.getCoreRowModel()],(o,n)=>Object.keys(o).length?K(e,n):{rows:[],flatRows:[],rowsById:{}},C(e.options,"debugTable")),e.getFilteredSelectedRowModel=m(()=>[e.getState().rowSelection,e.getFilteredRowModel()],(o,n)=>Object.keys(o).length?K(e,n):{rows:[],flatRows:[],rowsById:{}},C(e.options,"debugTable")),e.getGroupedSelectedRowModel=m(()=>[e.getState().rowSelection,e.getSortedRowModel()],(o,n)=>Object.keys(o).length?K(e,n):{rows:[],flatRows:[],rowsById:{}},C(e.options,"debugTable")),e.getIsAllRowsSelected=()=>{const o=e.getFilteredRowModel().flatRows,{rowSelection:n}=e.getState();let t=!!(o.length&&Object.keys(n).length);return t&&o.some(i=>i.getCanSelect()&&!n[i.id])&&(t=!1),t},e.getIsAllPageRowsSelected=()=>{const o=e.getPaginationRowModel().flatRows.filter(i=>i.getCanSelect()),{rowSelection:n}=e.getState();let t=!!o.length;return t&&o.some(i=>!n[i.id])&&(t=!1),t},e.getIsSomeRowsSelected=()=>{var o;const n=Object.keys((o=e.getState().rowSelection)!=null?o:{}).length;return n>0&&n<e.getFilteredRowModel().flatRows.length},e.getIsSomePageRowsSelected=()=>{const o=e.getPaginationRowModel().flatRows;return e.getIsAllPageRowsSelected()?!1:o.filter(n=>n.getCanSelect()).some(n=>n.getIsSelected()||n.getIsSomeSelected())},e.getToggleAllRowsSelectedHandler=()=>o=>{e.toggleAllRowsSelected(o.target.checked)},e.getToggleAllPageRowsSelectedHandler=()=>o=>{e.toggleAllPageRowsSelected(o.target.checked)}},createRow:(e,o)=>{e.toggleSelected=(n,t)=>{const i=e.getIsSelected();o.setRowSelection(r=>{var l;if(n=typeof n<"u"?n:!i,e.getCanSelect()&&i===n)return r;const u={...r};return W(u,e.id,n,(l=t==null?void 0:t.selectChildren)!=null?l:!0,o),u})},e.getIsSelected=()=>{const{rowSelection:n}=o.getState();return ee(e,n)},e.getIsSomeSelected=()=>{const{rowSelection:n}=o.getState();return Y(e,n)==="some"},e.getIsAllSubRowsSelected=()=>{const{rowSelection:n}=o.getState();return Y(e,n)==="all"},e.getCanSelect=()=>{var n;return typeof o.options.enableRowSelection=="function"?o.options.enableRowSelection(e):(n=o.options.enableRowSelection)!=null?n:!0},e.getCanSelectSubRows=()=>{var n;return typeof o.options.enableSubRowSelection=="function"?o.options.enableSubRowSelection(e):(n=o.options.enableSubRowSelection)!=null?n:!0},e.getCanMultiSelect=()=>{var n;return typeof o.options.enableMultiRowSelection=="function"?o.options.enableMultiRowSelection(e):(n=o.options.enableMultiRowSelection)!=null?n:!0},e.getToggleSelectedHandler=()=>{const n=e.getCanSelect();return t=>{var i;n&&e.toggleSelected((i=t.target)==null?void 0:i.checked)}}}},W=(e,o,n,t,i)=>{var r;const l=i.getRow(o,!0);n?(l.getCanMultiSelect()||Object.keys(e).forEach(u=>delete e[u]),l.getCanSelect()&&(e[o]=!0)):delete e[o],t&&(r=l.subRows)!=null&&r.length&&l.getCanSelectSubRows()&&l.subRows.forEach(u=>W(e,u.id,n,t,i))};function K(e,o){const n=e.getState().rowSelection,t=[],i={},r=function(l,u){return l.map(s=>{var a;const d=ee(s,n);if(d&&(t.push(s),i[s.id]=s),(a=s.subRows)!=null&&a.length&&(s={...s,subRows:r(s.subRows)}),d)return s}).filter(Boolean)};return{rows:r(o.rows),flatRows:t,rowsById:i}}function ee(e,o){var n;return(n=o[e.id])!=null?n:!1}function Y(e,o,n){var t;if(!((t=e.subRows)!=null&&t.length))return!1;let i=!0,r=!1;return e.subRows.forEach(l=>{if(!(r&&!i)&&(l.getCanSelect()&&(ee(l,o)?r=!0:i=!1),l.subRows&&l.subRows.length)){const u=Y(l,o);u==="all"?r=!0:(u==="some"&&(r=!0),i=!1)}}),i?"all":r?"some":!1}const Z=/([0-9]+)/gm,dn=(e,o,n)=>Ce(y(e.getValue(n)).toLowerCase(),y(o.getValue(n)).toLowerCase()),fn=(e,o,n)=>Ce(y(e.getValue(n)),y(o.getValue(n))),cn=(e,o,n)=>ne(y(e.getValue(n)).toLowerCase(),y(o.getValue(n)).toLowerCase()),pn=(e,o,n)=>ne(y(e.getValue(n)),y(o.getValue(n))),Sn=(e,o,n)=>{const t=e.getValue(n),i=o.getValue(n);return t>i?1:t<i?-1:0},mn=(e,o,n)=>ne(e.getValue(n),o.getValue(n));function ne(e,o){return e===o?0:e>o?1:-1}function y(e){return typeof e=="number"?isNaN(e)||e===1/0||e===-1/0?"":String(e):typeof e=="string"?e:""}function Ce(e,o){const n=e.split(Z).filter(Boolean),t=o.split(Z).filter(Boolean);for(;n.length&&t.length;){const i=n.shift(),r=t.shift(),l=parseInt(i,10),u=parseInt(r,10),s=[l,u].sort();if(isNaN(s[0])){if(i>r)return 1;if(r>i)return-1;continue}if(isNaN(s[1]))return isNaN(l)?-1:1;if(l>u)return 1;if(u>l)return-1}return n.length-t.length}const H={alphanumeric:dn,alphanumericCaseSensitive:fn,text:cn,textCaseSensitive:pn,datetime:Sn,basic:mn},Cn={getInitialState:e=>({sorting:[],...e}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:e=>({onSortingChange:v("sorting",e),isMultiSortEvent:o=>o.shiftKey}),createColumn:(e,o)=>{e.getAutoSortingFn=()=>{const n=o.getFilteredRowModel().flatRows.slice(10);let t=!1;for(const i of n){const r=i==null?void 0:i.getValue(e.id);if(Object.prototype.toString.call(r)==="[object Date]")return H.datetime;if(typeof r=="string"&&(t=!0,r.split(Z).length>1))return H.alphanumeric}return t?H.text:H.basic},e.getAutoSortDir=()=>{const n=o.getFilteredRowModel().flatRows[0];return typeof(n==null?void 0:n.getValue(e.id))=="string"?"asc":"desc"},e.getSortingFn=()=>{var n,t;if(!e)throw new Error;return O(e.columnDef.sortingFn)?e.columnDef.sortingFn:e.columnDef.sortingFn==="auto"?e.getAutoSortingFn():(n=(t=o.options.sortingFns)==null?void 0:t[e.columnDef.sortingFn])!=null?n:H[e.columnDef.sortingFn]},e.toggleSorting=(n,t)=>{const i=e.getNextSortingOrder(),r=typeof n<"u"&&n!==null;o.setSorting(l=>{const u=l==null?void 0:l.find(g=>g.id===e.id),s=l==null?void 0:l.findIndex(g=>g.id===e.id);let a=[],d,c=r?n:i==="desc";if(l!=null&&l.length&&e.getCanMultiSort()&&t?u?d="toggle":d="add":l!=null&&l.length&&s!==l.length-1?d="replace":u?d="toggle":d="replace",d==="toggle"&&(r||i||(d="remove")),d==="add"){var f;a=[...l,{id:e.id,desc:c}],a.splice(0,a.length-((f=o.options.maxMultiSortColCount)!=null?f:Number.MAX_SAFE_INTEGER))}else d==="toggle"?a=l.map(g=>g.id===e.id?{...g,desc:c}:g):d==="remove"?a=l.filter(g=>g.id!==e.id):a=[{id:e.id,desc:c}];return a})},e.getFirstSortDir=()=>{var n,t;return((n=(t=e.columnDef.sortDescFirst)!=null?t:o.options.sortDescFirst)!=null?n:e.getAutoSortDir()==="desc")?"desc":"asc"},e.getNextSortingOrder=n=>{var t,i;const r=e.getFirstSortDir(),l=e.getIsSorted();return l?l!==r&&((t=o.options.enableSortingRemoval)==null||t)&&(!(n&&(i=o.options.enableMultiRemove)!=null)||i)?!1:l==="desc"?"asc":"desc":r},e.getCanSort=()=>{var n,t;return((n=e.columnDef.enableSorting)!=null?n:!0)&&((t=o.options.enableSorting)!=null?t:!0)&&!!e.accessorFn},e.getCanMultiSort=()=>{var n,t;return(n=(t=e.columnDef.enableMultiSort)!=null?t:o.options.enableMultiSort)!=null?n:!!e.accessorFn},e.getIsSorted=()=>{var n;const t=(n=o.getState().sorting)==null?void 0:n.find(i=>i.id===e.id);return t?t.desc?"desc":"asc":!1},e.getSortIndex=()=>{var n,t;return(n=(t=o.getState().sorting)==null?void 0:t.findIndex(i=>i.id===e.id))!=null?n:-1},e.clearSorting=()=>{o.setSorting(n=>n!=null&&n.length?n.filter(t=>t.id!==e.id):[])},e.getToggleSortingHandler=()=>{const n=e.getCanSort();return t=>{n&&(t.persist==null||t.persist(),e.toggleSorting==null||e.toggleSorting(void 0,e.getCanMultiSort()?o.options.isMultiSortEvent==null?void 0:o.options.isMultiSortEvent(t):!1))}}},createTable:e=>{e.setSorting=o=>e.options.onSortingChange==null?void 0:e.options.onSortingChange(o),e.resetSorting=o=>{var n,t;e.setSorting(o?[]:(n=(t=e.initialState)==null?void 0:t.sorting)!=null?n:[])},e.getPreSortedRowModel=()=>e.getGroupedRowModel(),e.getSortedRowModel=()=>(!e._getSortedRowModel&&e.options.getSortedRowModel&&(e._getSortedRowModel=e.options.getSortedRowModel(e)),e.options.manualSorting||!e._getSortedRowModel?e.getPreSortedRowModel():e._getSortedRowModel())}},Rn=[Oe,on,be,en,Be,Te,rn,ln,Cn,Ye,un,sn,an,gn,nn];function wn(e){var o,n;const t=[...Rn,...(o=e._features)!=null?o:[]];let i={_features:t};const r=i._features.reduce((f,g)=>Object.assign(f,g.getDefaultOptions==null?void 0:g.getDefaultOptions(i)),{}),l=f=>i.options.mergeOptions?i.options.mergeOptions(r,f):{...r,...f};let s={...{},...(n=e.initialState)!=null?n:{}};i._features.forEach(f=>{var g;s=(g=f.getInitialState==null?void 0:f.getInitialState(s))!=null?g:s});const a=[];let d=!1;const c={_features:t,options:{...r,...e},initialState:s,_queue:f=>{a.push(f),d||(d=!0,Promise.resolve().then(()=>{for(;a.length;)a.shift()();d=!1}).catch(g=>setTimeout(()=>{throw g})))},reset:()=>{i.setState(i.initialState)},setOptions:f=>{const g=I(f,i.options);i.options=l(g)},getState:()=>i.options.state,setState:f=>{i.options.onStateChange==null||i.options.onStateChange(f)},_getRowId:(f,g,p)=>{var S;return(S=i.options.getRowId==null?void 0:i.options.getRowId(f,g,p))!=null?S:`${p?[p.id,g].join("."):g}`},getCoreRowModel:()=>(i._getCoreRowModel||(i._getCoreRowModel=i.options.getCoreRowModel(i)),i._getCoreRowModel()),getRowModel:()=>i.getPaginationRowModel(),getRow:(f,g)=>{let p=(g?i.getPrePaginationRowModel():i.getRowModel()).rowsById[f];if(!p&&(p=i.getCoreRowModel().rowsById[f],!p))throw new Error;return p},_getDefaultColumnDef:m(()=>[i.options.defaultColumn],f=>{var g;return f=(g=f)!=null?g:{},{header:p=>{const S=p.header.column.columnDef;return S.accessorKey?S.accessorKey:S.accessorFn?S.id:null},cell:p=>{var S,w;return(S=(w=p.renderValue())==null||w.toString==null?void 0:w.toString())!=null?S:null},...i._features.reduce((p,S)=>Object.assign(p,S.getDefaultColumnDef==null?void 0:S.getDefaultColumnDef()),{}),...f}},C(e,"debugColumns")),_getColumnDefs:()=>i.options.columns,getAllColumns:m(()=>[i._getColumnDefs()],f=>{const g=function(p,S,w){return w===void 0&&(w=0),p.map(R=>{const _=Le(i,R,w,S),F=R;return _.columns=F.columns?g(F.columns,_,w+1):[],_})};return g(f)},C(e,"debugColumns")),getAllFlatColumns:m(()=>[i.getAllColumns()],f=>f.flatMap(g=>g.getFlatColumns()),C(e,"debugColumns")),_getAllFlatColumnsById:m(()=>[i.getAllFlatColumns()],f=>f.reduce((g,p)=>(g[p.id]=p,g),{}),C(e,"debugColumns")),getAllLeafColumns:m(()=>[i.getAllColumns(),i._getOrderColumnsFn()],(f,g)=>{let p=f.flatMap(S=>S.getLeafColumns());return g(p)},C(e,"debugColumns")),getColumn:f=>i._getAllFlatColumnsById()[f]};Object.assign(i,c);for(let f=0;f<i._features.length;f++){const g=i._features[f];g==null||g.createTable==null||g.createTable(i)}return i}function hn(){return e=>m(()=>[e.options.data],o=>{const n={rows:[],flatRows:[],rowsById:{}},t=function(i,r,l){r===void 0&&(r=0);const u=[];for(let a=0;a<i.length;a++){const d=je(e,e._getRowId(i[a],a,l),i[a],a,r,void 0,l==null?void 0:l.id);if(n.flatRows.push(d),n.rowsById[d.id]=d,u.push(d),e.options.getSubRows){var s;d.originalSubRows=e.options.getSubRows(i[a],a),(s=d.originalSubRows)!=null&&s.length&&(d.subRows=t(d.originalSubRows,r+1,d))}}return u};return n.rows=t(o),n},C(e.options,"debugTable","getRowModel",()=>e._autoResetPageIndex()))}function _n(e){const o=[],n=t=>{var i;o.push(t),(i=t.subRows)!=null&&i.length&&t.getIsExpanded()&&t.subRows.forEach(n)};return e.rows.forEach(n),{rows:o,flatRows:e.flatRows,rowsById:e.rowsById}}function Fn(e){return o=>m(()=>[o.getState().pagination,o.getPrePaginationRowModel(),o.options.paginateExpandedRows?void 0:o.getState().expanded],(n,t)=>{if(!t.rows.length)return t;const{pageSize:i,pageIndex:r}=n;let{rows:l,flatRows:u,rowsById:s}=t;const a=i*r,d=a+i;l=l.slice(a,d);let c;o.options.paginateExpandedRows?c={rows:l,flatRows:u,rowsById:s}:c=_n({rows:l,flatRows:u,rowsById:s}),c.flatRows=[];const f=g=>{c.flatRows.push(g),g.subRows.length&&g.subRows.forEach(f)};return c.rows.forEach(f),c},C(o.options,"debugTable"))}function $n(){return e=>m(()=>[e.getState().sorting,e.getPreSortedRowModel()],(o,n)=>{if(!n.rows.length||!(o!=null&&o.length))return n;const t=e.getState().sorting,i=[],r=t.filter(s=>{var a;return(a=e.getColumn(s.id))==null?void 0:a.getCanSort()}),l={};r.forEach(s=>{const a=e.getColumn(s.id);a&&(l[s.id]={sortUndefined:a.columnDef.sortUndefined,invertSorting:a.columnDef.invertSorting,sortingFn:a.getSortingFn()})});const u=s=>{const a=s.map(d=>({...d}));return a.sort((d,c)=>{for(let g=0;g<r.length;g+=1){var f;const p=r[g],S=l[p.id],w=S.sortUndefined,R=(f=p==null?void 0:p.desc)!=null?f:!1;let _=0;if(w){const F=d.getValue(p.id),M=c.getValue(p.id),x=F===void 0,D=M===void 0;if(x||D){if(w==="first")return x?-1:1;if(w==="last")return x?1:-1;_=x&&D?0:x?w:-w}}if(_===0&&(_=S.sortingFn(d,c,p.id)),_!==0)return R&&(_*=-1),S.invertSorting&&(_*=-1),_}return d.index-c.index}),a.forEach(d=>{var c;i.push(d),(c=d.subRows)!=null&&c.length&&(d.subRows=u(d.subRows))}),a};return{rows:u(n.rows),flatRows:i,rowsById:n.rowsById}},C(e.options,"debugTable","getSortedRowModel",()=>e._autoResetPageIndex()))}/**
   * react-table
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function se(e,o){return e?xn(e)?E.createElement(e,o):e:null}function xn(e){return vn(e)||typeof e=="function"||Pn(e)}function vn(e){return typeof e=="function"&&(()=>{const o=Object.getPrototypeOf(e);return o.prototype&&o.prototype.isReactComponent})()}function Pn(e){return typeof e=="object"&&typeof e.$$typeof=="symbol"&&["react.memo","react.forward_ref"].includes(e.$$typeof.description)}function Mn(e){const o={state:{},onStateChange:()=>{},renderFallbackValue:null,...e},[n]=E.useState(()=>({current:wn(o)})),[t,i]=E.useState(()=>n.current.initialState);return n.current.setOptions(r=>({...r,...e,state:{...t,...e.state},onStateChange:l=>{i(l),e.onStateChange==null||e.onStateChange(l)}})),n.current}const Vn=({data:e})=>{var s;const{index:o,columns:n,data:t}=e,i=E.useMemo(()=>n.map(a=>({accessorKey:a,header:({column:d})=>{const c=d.getIsSorted();return h.jsxs("div",{className:"flex items-center cursor-pointer",onClick:()=>d.toggleSorting(),children:[a,c==="asc"&&h.jsx($e,{className:"ml-2 !size-3"}),c==="desc"&&h.jsx(xe,{className:"ml-2 !size-3"})]})}})),[n]),r=E.useMemo(()=>t.map((a,d)=>{const c={id:o[d]};return n.forEach((f,g)=>{c[f]=a[g]}),c}),[t,n,o]),l=Mn({data:r,columns:i,getCoreRowModel:hn(),getPaginationRowModel:Fn(),getSortedRowModel:$n(),initialState:{pagination:{pageSize:10}}}),u=E.useCallback(()=>Array.from({length:l.getPageCount()},(a,d)=>h.jsx(j,{children:h.jsx(ve,{onClick:()=>l.setPageIndex(d),isActive:l.getState().pagination.pageIndex===d,children:d+1})},d)),[l.getPageCount(),l.getState().pagination.pageIndex]);return h.jsxs("div",{className:"flex flex-col gap-2 h-full overflow-y-auto dataframe",children:[h.jsx("div",{className:"rounded-md border overflow-y-auto",children:h.jsxs(Pe,{children:[h.jsx(Me,{children:l.getHeaderGroups().map(a=>h.jsx(B,{children:a.headers.map(d=>h.jsx(Ve,{children:d.isPlaceholder?null:se(d.column.columnDef.header,d.getContext())},d.id))},a.id))}),h.jsx(Ie,{children:(s=l.getRowModel().rows)!=null&&s.length?l.getRowModel().rows.map(a=>h.jsx(B,{children:a.getVisibleCells().map(d=>h.jsx(re,{children:se(d.column.columnDef.cell,d.getContext())},d.id))},a.id)):h.jsx(B,{children:h.jsx(re,{colSpan:n.length,className:"h-24 text-center",children:"No results."})})})]})}),h.jsx(ye,{children:h.jsxs(Ee,{className:"ml-auto",children:[h.jsx(j,{children:h.jsx(De,{onClick:()=>l.previousPage(),className:l.getCanPreviousPage()?"cursor-pointer":"pointer-events-none opacity-50"})}),u(),h.jsx(j,{children:h.jsx(He,{onClick:()=>l.nextPage(),className:l.getCanNextPage()?"cursor-pointer":"pointer-events-none opacity-50"})})]})})]})};function yn({element:e}){const{data:o,isLoading:n,error:t}=he(e.url||null),i=E.useMemo(()=>{if(o)return JSON.parse(o)},[o]);return n?h.jsx("div",{className:"flex items-center justify-center h-full w-full bg-muted",children:h.jsx(_e,{})}):t?h.jsx(Fe,{variant:"error",children:t.message}):h.jsx(Vn,{data:i})}export{yn as default};
//# sourceMappingURL=Dataframe-annO0W8G.js.map
