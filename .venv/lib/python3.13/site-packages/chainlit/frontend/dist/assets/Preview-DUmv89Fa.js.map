{"version": 3, "file": "Preview-DUmv89Fa.js", "sources": ["../../node_modules/.pnpm/react-player@2.16.0_react@18.3.1/node_modules/react-player/lib/Preview.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar Preview_exports = {};\n__export(Preview_exports, {\n  default: () => Preview\n});\nmodule.exports = __toCommonJS(Preview_exports);\nvar import_react = __toESM(require(\"react\"));\nconst ICON_SIZE = \"64px\";\nconst cache = {};\nclass Preview extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"mounted\", false);\n    __publicField(this, \"state\", {\n      image: null\n    });\n    __publicField(this, \"handleKeyPress\", (e) => {\n      if (e.key === \"Enter\" || e.key === \" \") {\n        this.props.onClick();\n      }\n    });\n  }\n  componentDidMount() {\n    this.mounted = true;\n    this.fetchImage(this.props);\n  }\n  componentDidUpdate(prevProps) {\n    const { url, light } = this.props;\n    if (prevProps.url !== url || prevProps.light !== light) {\n      this.fetchImage(this.props);\n    }\n  }\n  componentWillUnmount() {\n    this.mounted = false;\n  }\n  fetchImage({ url, light, oEmbedUrl }) {\n    if (import_react.default.isValidElement(light)) {\n      return;\n    }\n    if (typeof light === \"string\") {\n      this.setState({ image: light });\n      return;\n    }\n    if (cache[url]) {\n      this.setState({ image: cache[url] });\n      return;\n    }\n    this.setState({ image: null });\n    return window.fetch(oEmbedUrl.replace(\"{url}\", url)).then((response) => response.json()).then((data) => {\n      if (data.thumbnail_url && this.mounted) {\n        const image = data.thumbnail_url.replace(\"height=100\", \"height=480\").replace(\"-d_295x166\", \"-d_640\");\n        this.setState({ image });\n        cache[url] = image;\n      }\n    });\n  }\n  render() {\n    const { light, onClick, playIcon, previewTabIndex, previewAriaLabel } = this.props;\n    const { image } = this.state;\n    const isElement = import_react.default.isValidElement(light);\n    const flexCenter = {\n      display: \"flex\",\n      alignItems: \"center\",\n      justifyContent: \"center\"\n    };\n    const styles = {\n      preview: {\n        width: \"100%\",\n        height: \"100%\",\n        backgroundImage: image && !isElement ? `url(${image})` : void 0,\n        backgroundSize: \"cover\",\n        backgroundPosition: \"center\",\n        cursor: \"pointer\",\n        ...flexCenter\n      },\n      shadow: {\n        background: \"radial-gradient(rgb(0, 0, 0, 0.3), rgba(0, 0, 0, 0) 60%)\",\n        borderRadius: ICON_SIZE,\n        width: ICON_SIZE,\n        height: ICON_SIZE,\n        position: isElement ? \"absolute\" : void 0,\n        ...flexCenter\n      },\n      playIcon: {\n        borderStyle: \"solid\",\n        borderWidth: \"16px 0 16px 26px\",\n        borderColor: \"transparent transparent transparent white\",\n        marginLeft: \"7px\"\n      }\n    };\n    const defaultPlayIcon = /* @__PURE__ */ import_react.default.createElement(\"div\", { style: styles.shadow, className: \"react-player__shadow\" }, /* @__PURE__ */ import_react.default.createElement(\"div\", { style: styles.playIcon, className: \"react-player__play-icon\" }));\n    return /* @__PURE__ */ import_react.default.createElement(\n      \"div\",\n      {\n        style: styles.preview,\n        className: \"react-player__preview\",\n        onClick,\n        tabIndex: previewTabIndex,\n        onKeyPress: this.handleKeyPress,\n        ...previewAriaLabel ? { \"aria-label\": previewAriaLabel } : {}\n      },\n      isElement ? light : null,\n      playIcon || defaultPlayIcon\n    );\n  }\n}\n"], "names": ["__create", "__defProp", "__getOwnPropDesc", "__getOwnPropNames", "__getProtoOf", "__hasOwnProp", "__defNormalProp", "obj", "key", "value", "__export", "target", "all", "name", "__copyProps", "to", "from", "except", "desc", "__toESM", "mod", "isNodeMode", "__toCommonJS", "__publicField", "Preview_exports", "Preview", "Preview_1", "import_react", "require$$0", "ICON_SIZE", "cache", "prevProps", "url", "light", "oEmbedUrl", "response", "data", "image", "onClick", "playIcon", "previewTabIndex", "previewAriaLabel", "isElement", "flexCenter", "styles", "defaultPlayIcon"], "mappings": "qYAAA,IAAIA,EAAW,OAAO,OAClBC,EAAY,OAAO,eACnBC,EAAmB,OAAO,yBAC1BC,EAAoB,OAAO,oBAC3BC,EAAe,OAAO,eACtBC,EAAe,OAAO,UAAU,eAChCC,EAAkB,CAACC,EAAKC,EAAKC,IAAUD,KAAOD,EAAMN,EAAUM,EAAKC,EAAK,CAAE,WAAY,GAAM,aAAc,GAAM,SAAU,GAAM,MAAAC,CAAK,CAAE,EAAIF,EAAIC,CAAG,EAAIC,EACtJC,EAAW,CAACC,EAAQC,IAAQ,CAC9B,QAASC,KAAQD,EACfX,EAAUU,EAAQE,EAAM,CAAE,IAAKD,EAAIC,CAAI,EAAG,WAAY,GAAM,CAChE,EACIC,EAAc,CAACC,EAAIC,EAAMC,EAAQC,IAAS,CAC5C,GAAIF,GAAQ,OAAOA,GAAS,UAAY,OAAOA,GAAS,WACtD,QAASR,KAAOL,EAAkBa,CAAI,EAChC,CAACX,EAAa,KAAKU,EAAIP,CAAG,GAAKA,IAAQS,GACzChB,EAAUc,EAAIP,EAAK,CAAE,IAAK,IAAMQ,EAAKR,CAAG,EAAG,WAAY,EAAEU,EAAOhB,EAAiBc,EAAMR,CAAG,IAAMU,EAAK,WAAY,EAEvH,OAAOH,CACT,EACII,EAAU,CAACC,EAAKC,EAAYV,KAAYA,EAASS,GAAO,KAAOpB,EAASI,EAAagB,CAAG,CAAC,EAAI,CAAE,EAAEN,EAKrF,CAACM,GAAO,CAACA,EAAI,WAAanB,EAAUU,EAAQ,UAAW,CAAE,MAAOS,EAAK,WAAY,EAAI,CAAE,EAAIT,EACzGS,CACF,GACIE,EAAgBF,GAAQN,EAAYb,EAAU,CAAA,EAAI,aAAc,CAAE,MAAO,EAAM,CAAA,EAAGmB,CAAG,EACrFG,EAAgB,CAAChB,EAAKC,EAAKC,KAC7BH,EAAgBC,EAAK,OAAOC,GAAQ,SAAWA,EAAM,GAAKA,EAAKC,CAAK,EAC7DA,GAELe,EAAkB,CAAE,EACxBd,EAASc,EAAiB,CACxB,QAAS,IAAMC,CACjB,CAAC,EACD,IAAAC,EAAiBJ,EAAaE,CAAe,EACzCG,EAAeR,EAAQS,CAAgB,EAC3C,MAAMC,EAAY,OACZC,EAAQ,CAAE,EAChB,MAAML,UAAgBE,EAAa,SAAU,CAC3C,aAAc,CACZ,MAAM,GAAG,SAAS,EAClBJ,EAAc,KAAM,UAAW,EAAK,EACpCA,EAAc,KAAM,QAAS,CAC3B,MAAO,IACb,CAAK,EACDA,EAAc,KAAM,iBAAmB,GAAM,EACvC,EAAE,MAAQ,SAAW,EAAE,MAAQ,MACjC,KAAK,MAAM,QAAS,CAE5B,CAAK,CACL,CACE,mBAAoB,CAClB,KAAK,QAAU,GACf,KAAK,WAAW,KAAK,KAAK,CAC9B,CACE,mBAAmBQ,EAAW,CAC5B,KAAM,CAAE,IAAAC,EAAK,MAAAC,CAAO,EAAG,KAAK,OACxBF,EAAU,MAAQC,GAAOD,EAAU,QAAUE,IAC/C,KAAK,WAAW,KAAK,KAAK,CAEhC,CACE,sBAAuB,CACrB,KAAK,QAAU,EACnB,CACE,WAAW,CAAE,IAAAD,EAAK,MAAAC,EAAO,UAAAC,CAAS,EAAI,CACpC,GAAI,CAAAP,EAAa,QAAQ,eAAeM,CAAK,EAG7C,IAAI,OAAOA,GAAU,SAAU,CAC7B,KAAK,SAAS,CAAE,MAAOA,CAAK,CAAE,EAC9B,MACN,CACI,GAAIH,EAAME,CAAG,EAAG,CACd,KAAK,SAAS,CAAE,MAAOF,EAAME,CAAG,CAAC,CAAE,EACnC,MACN,CACI,YAAK,SAAS,CAAE,MAAO,IAAI,CAAE,EACtB,OAAO,MAAME,EAAU,QAAQ,QAASF,CAAG,CAAC,EAAE,KAAMG,GAAaA,EAAS,KAAM,CAAA,EAAE,KAAMC,GAAS,CACtG,GAAIA,EAAK,eAAiB,KAAK,QAAS,CACtC,MAAMC,EAAQD,EAAK,cAAc,QAAQ,aAAc,YAAY,EAAE,QAAQ,aAAc,QAAQ,EACnG,KAAK,SAAS,CAAE,MAAAC,EAAO,EACvBP,EAAME,CAAG,EAAIK,CACrB,CACA,CAAK,EACL,CACE,QAAS,CACP,KAAM,CAAE,MAAAJ,EAAO,QAAAK,EAAS,SAAAC,EAAU,gBAAAC,EAAiB,iBAAAC,CAAgB,EAAK,KAAK,MACvE,CAAE,MAAAJ,GAAU,KAAK,MACjBK,EAAYf,EAAa,QAAQ,eAAeM,CAAK,EACrDU,EAAa,CACjB,QAAS,OACT,WAAY,SACZ,eAAgB,QACjB,EACKC,EAAS,CACb,QAAS,CACP,MAAO,OACP,OAAQ,OACR,gBAAiBP,GAAS,CAACK,EAAY,OAAOL,CAAK,IAAM,OACzD,eAAgB,QAChB,mBAAoB,SACpB,OAAQ,UACR,GAAGM,CACJ,EACD,OAAQ,CACN,WAAY,2DACZ,aAAcd,EACd,MAAOA,EACP,OAAQA,EACR,SAAUa,EAAY,WAAa,OACnC,GAAGC,CACJ,EACD,SAAU,CACR,YAAa,QACb,YAAa,mBACb,YAAa,4CACb,WAAY,KACpB,CACK,EACKE,EAAkClB,EAAa,QAAQ,cAAc,MAAO,CAAE,MAAOiB,EAAO,OAAQ,UAAW,wBAA0CjB,EAAa,QAAQ,cAAc,MAAO,CAAE,MAAOiB,EAAO,SAAU,UAAW,yBAAyB,CAAE,CAAC,EAC1Q,OAAuBjB,EAAa,QAAQ,cAC1C,MACA,CACE,MAAOiB,EAAO,QACd,UAAW,wBACX,QAAAN,EACA,SAAUE,EACV,WAAY,KAAK,eACjB,GAAGC,EAAmB,CAAE,aAAcA,GAAqB,CAAA,CAC5D,EACDC,EAAYT,EAAQ,KACpBM,GAAYM,CACb,CACL,CACA", "x_google_ignoreList": [0]}