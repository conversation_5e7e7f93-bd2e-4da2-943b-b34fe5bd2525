{"version": 3, "file": "Kaltura-DSxDIFbE.js", "sources": ["../../node_modules/.pnpm/react-player@2.16.0_react@18.3.1/node_modules/react-player/lib/players/Kaltura.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar Kaltura_exports = {};\n__export(Kaltura_exports, {\n  default: () => Kaltura\n});\nmodule.exports = __toCommonJS(Kaltura_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://cdn.embed.ly/player-0.1.0.min.js\";\nconst SDK_GLOBAL = \"playerjs\";\nclass Kaltura extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"callPlayer\", import_utils.callPlayer);\n    __publicField(this, \"duration\", null);\n    __publicField(this, \"currentTime\", null);\n    __publicField(this, \"secondsLoaded\", null);\n    __publicField(this, \"mute\", () => {\n      this.callPlayer(\"mute\");\n    });\n    __publicField(this, \"unmute\", () => {\n      this.callPlayer(\"unmute\");\n    });\n    __publicField(this, \"ref\", (iframe) => {\n      this.iframe = iframe;\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n  }\n  load(url) {\n    (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL).then((playerjs) => {\n      if (!this.iframe)\n        return;\n      this.player = new playerjs.Player(this.iframe);\n      this.player.on(\"ready\", () => {\n        setTimeout(() => {\n          this.player.isReady = true;\n          this.player.setLoop(this.props.loop);\n          if (this.props.muted) {\n            this.player.mute();\n          }\n          this.addListeners(this.player, this.props);\n          this.props.onReady();\n        }, 500);\n      });\n    }, this.props.onError);\n  }\n  addListeners(player, props) {\n    player.on(\"play\", props.onPlay);\n    player.on(\"pause\", props.onPause);\n    player.on(\"ended\", props.onEnded);\n    player.on(\"error\", props.onError);\n    player.on(\"timeupdate\", ({ duration, seconds }) => {\n      this.duration = duration;\n      this.currentTime = seconds;\n    });\n  }\n  play() {\n    this.callPlayer(\"play\");\n  }\n  pause() {\n    this.callPlayer(\"pause\");\n  }\n  stop() {\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.callPlayer(\"setCurrentTime\", seconds);\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.callPlayer(\"setVolume\", fraction);\n  }\n  setLoop(loop) {\n    this.callPlayer(\"setLoop\", loop);\n  }\n  getDuration() {\n    return this.duration;\n  }\n  getCurrentTime() {\n    return this.currentTime;\n  }\n  getSecondsLoaded() {\n    return this.secondsLoaded;\n  }\n  render() {\n    const style = {\n      width: \"100%\",\n      height: \"100%\"\n    };\n    return /* @__PURE__ */ import_react.default.createElement(\n      \"iframe\",\n      {\n        ref: this.ref,\n        src: this.props.url,\n        frameBorder: \"0\",\n        scrolling: \"no\",\n        style,\n        allow: \"encrypted-media; autoplay; fullscreen;\",\n        referrerPolicy: \"no-referrer-when-downgrade\"\n      }\n    );\n  }\n}\n__publicField(Kaltura, \"displayName\", \"Kaltura\");\n__publicField(Kaltura, \"canPlay\", import_patterns.canPlay.kaltura);\n"], "names": ["__create", "__defProp", "__getOwnPropDesc", "__getOwnPropNames", "__getProtoOf", "__hasOwnProp", "__defNormalProp", "obj", "key", "value", "__export", "target", "all", "name", "__copyProps", "to", "from", "except", "desc", "__toESM", "mod", "isNodeMode", "__toCommonJS", "__publicField", "Kaltura_exports", "<PERSON><PERSON><PERSON>", "Kaltura_1", "import_react", "require$$0", "import_utils", "require$$1", "import_patterns", "require$$2", "SDK_URL", "SDK_GLOBAL", "iframe", "url", "playerjs", "player", "props", "duration", "seconds", "keepPlaying", "fraction", "loop", "style"], "mappings": "mZAAA,IAAIA,EAAW,OAAO,OAClBC,EAAY,OAAO,eACnBC,EAAmB,OAAO,yBAC1BC,EAAoB,OAAO,oBAC3BC,EAAe,OAAO,eACtBC,EAAe,OAAO,UAAU,eAChCC,EAAkB,CAACC,EAAKC,EAAKC,IAAUD,KAAOD,EAAMN,EAAUM,EAAKC,EAAK,CAAE,WAAY,GAAM,aAAc,GAAM,SAAU,GAAM,MAAAC,CAAK,CAAE,EAAIF,EAAIC,CAAG,EAAIC,EACtJC,EAAW,CAACC,EAAQC,IAAQ,CAC9B,QAASC,KAAQD,EACfX,EAAUU,EAAQE,EAAM,CAAE,IAAKD,EAAIC,CAAI,EAAG,WAAY,GAAM,CAChE,EACIC,EAAc,CAACC,EAAIC,EAAMC,EAAQC,IAAS,CAC5C,GAAIF,GAAQ,OAAOA,GAAS,UAAY,OAAOA,GAAS,WACtD,QAASR,KAAOL,EAAkBa,CAAI,EAChC,CAACX,EAAa,KAAKU,EAAIP,CAAG,GAAKA,IAAQS,GACzChB,EAAUc,EAAIP,EAAK,CAAE,IAAK,IAAMQ,EAAKR,CAAG,EAAG,WAAY,EAAEU,EAAOhB,EAAiBc,EAAMR,CAAG,IAAMU,EAAK,WAAY,EAEvH,OAAOH,CACT,EACII,EAAU,CAACC,EAAKC,EAAYV,KAAYA,EAASS,GAAO,KAAOpB,EAASI,EAAagB,CAAG,CAAC,EAAI,CAAE,EAAEN,EAKrF,CAACM,GAAO,CAACA,EAAI,WAAanB,EAAUU,EAAQ,UAAW,CAAE,MAAOS,EAAK,WAAY,EAAI,CAAE,EAAIT,EACzGS,CACF,GACIE,EAAgBF,GAAQN,EAAYb,EAAU,CAAA,EAAI,aAAc,CAAE,MAAO,EAAM,CAAA,EAAGmB,CAAG,EACrFG,EAAgB,CAAChB,EAAKC,EAAKC,KAC7BH,EAAgBC,EAAK,OAAOC,GAAQ,SAAWA,EAAM,GAAKA,EAAKC,CAAK,EAC7DA,GAELe,EAAkB,CAAE,EACxBd,EAASc,EAAiB,CACxB,QAAS,IAAMC,CACjB,CAAC,EACD,IAAAC,EAAiBJ,EAAaE,CAAe,EACzCG,EAAeR,EAAQS,CAAgB,EACvCC,EAAeC,EACfC,EAAkBC,EACtB,MAAMC,EAAU,2CACVC,EAAa,WACnB,MAAMT,UAAgBE,EAAa,SAAU,CAC3C,aAAc,CACZ,MAAM,GAAG,SAAS,EAClBJ,EAAc,KAAM,aAAcM,EAAa,UAAU,EACzDN,EAAc,KAAM,WAAY,IAAI,EACpCA,EAAc,KAAM,cAAe,IAAI,EACvCA,EAAc,KAAM,gBAAiB,IAAI,EACzCA,EAAc,KAAM,OAAQ,IAAM,CAChC,KAAK,WAAW,MAAM,CAC5B,CAAK,EACDA,EAAc,KAAM,SAAU,IAAM,CAClC,KAAK,WAAW,QAAQ,CAC9B,CAAK,EACDA,EAAc,KAAM,MAAQY,GAAW,CACrC,KAAK,OAASA,CACpB,CAAK,CACL,CACE,mBAAoB,CAClB,KAAK,MAAM,SAAW,KAAK,MAAM,QAAQ,IAAI,CACjD,CACE,KAAKC,EAAK,IACJP,EAAa,QAAQI,EAASC,CAAU,EAAE,KAAMG,GAAa,CAC1D,KAAK,SAEV,KAAK,OAAS,IAAIA,EAAS,OAAO,KAAK,MAAM,EAC7C,KAAK,OAAO,GAAG,QAAS,IAAM,CAC5B,WAAW,IAAM,CACf,KAAK,OAAO,QAAU,GACtB,KAAK,OAAO,QAAQ,KAAK,MAAM,IAAI,EAC/B,KAAK,MAAM,OACb,KAAK,OAAO,KAAM,EAEpB,KAAK,aAAa,KAAK,OAAQ,KAAK,KAAK,EACzC,KAAK,MAAM,QAAS,CACrB,EAAE,GAAG,CACd,CAAO,EACP,EAAO,KAAK,MAAM,OAAO,CACzB,CACE,aAAaC,EAAQC,EAAO,CAC1BD,EAAO,GAAG,OAAQC,EAAM,MAAM,EAC9BD,EAAO,GAAG,QAASC,EAAM,OAAO,EAChCD,EAAO,GAAG,QAASC,EAAM,OAAO,EAChCD,EAAO,GAAG,QAASC,EAAM,OAAO,EAChCD,EAAO,GAAG,aAAc,CAAC,CAAE,SAAAE,EAAU,QAAAC,CAAO,IAAO,CACjD,KAAK,SAAWD,EAChB,KAAK,YAAcC,CACzB,CAAK,CACL,CACE,MAAO,CACL,KAAK,WAAW,MAAM,CAC1B,CACE,OAAQ,CACN,KAAK,WAAW,OAAO,CAC3B,CACE,MAAO,CACT,CACE,OAAOA,EAASC,EAAc,GAAM,CAClC,KAAK,WAAW,iBAAkBD,CAAO,EACpCC,GACH,KAAK,MAAO,CAElB,CACE,UAAUC,EAAU,CAClB,KAAK,WAAW,YAAaA,CAAQ,CACzC,CACE,QAAQC,EAAM,CACZ,KAAK,WAAW,UAAWA,CAAI,CACnC,CACE,aAAc,CACZ,OAAO,KAAK,QAChB,CACE,gBAAiB,CACf,OAAO,KAAK,WAChB,CACE,kBAAmB,CACjB,OAAO,KAAK,aAChB,CACE,QAAS,CACP,MAAMC,EAAQ,CACZ,MAAO,OACP,OAAQ,MACT,EACD,OAAuBlB,EAAa,QAAQ,cAC1C,SACA,CACE,IAAK,KAAK,IACV,IAAK,KAAK,MAAM,IAChB,YAAa,IACb,UAAW,KACX,MAAAkB,EACA,MAAO,yCACP,eAAgB,4BACxB,CACK,CACL,CACA,CACAtB,EAAcE,EAAS,cAAe,SAAS,EAC/CF,EAAcE,EAAS,UAAWM,EAAgB,QAAQ,OAAO", "x_google_ignoreList": [0]}