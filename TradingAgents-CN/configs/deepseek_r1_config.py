"""
DeepSeek R1 专用配置
针对DeepSeek R1推理模型优化的配置文件
"""

import os
from tradingagents.default_config import DEFAULT_CONFIG

# DeepSeek R1 基础配置
DEEPSEEK_R1_CONFIG = DEFAULT_CONFIG.copy()
DEEPSEEK_R1_CONFIG.update({
    # LLM 提供商设置
    "llm_provider": "deepseek",
    "backend_url": "https://api.deepseek.com/v1",
    
    # DeepSeek R1 模型配置
    "deep_think_llm": "deepseek-r1",                    # 深度思考使用R1
    "quick_think_llm": "deepseek-r1-distill-llama-70b", # 快速思考使用蒸馏版
    
    # 推理参数优化 (针对R1模型特点)
    "max_debate_rounds": 2,                             # R1模型推理能力强，适度辩论
    "max_risk_discuss_rounds": 2,                       # 风险讨论轮数
    "max_recur_limit": 150,                             # 增加递归限制
    
    # 工具设置
    "online_tools": True,
    "memory_enabled": True,
})

# DeepSeek R1 高性能配置 (适合专业分析)
DEEPSEEK_R1_PRO_CONFIG = DEEPSEEK_R1_CONFIG.copy()
DEEPSEEK_R1_PRO_CONFIG.update({
    # 全部使用R1模型，确保最高质量
    "deep_think_llm": "deepseek-r1",
    "quick_think_llm": "deepseek-r1",
    
    # 增强分析深度
    "max_debate_rounds": 3,
    "max_risk_discuss_rounds": 3,
    
    # 完整分析师团队
    "analysts": ["market", "fundamentals", "news", "social"],
    "researchers": ["bull", "bear"],
})

# DeepSeek R1 快速配置 (适合实时分析)
DEEPSEEK_R1_FAST_CONFIG = DEEPSEEK_R1_CONFIG.copy()
DEEPSEEK_R1_FAST_CONFIG.update({
    # 全部使用蒸馏版，提高速度
    "deep_think_llm": "deepseek-r1-distill-llama-70b",
    "quick_think_llm": "deepseek-r1-distill-llama-70b",
    
    # 减少分析轮数
    "max_debate_rounds": 1,
    "max_risk_discuss_rounds": 1,
    
    # 精简分析师团队
    "analysts": ["market", "news"],
    "researchers": ["bull"],
})

# DeepSeek R1 混合配置 (平衡性能和成本)
DEEPSEEK_R1_HYBRID_CONFIG = DEEPSEEK_R1_CONFIG.copy()
DEEPSEEK_R1_HYBRID_CONFIG.update({
    # 深度思考用R1，快速思考用蒸馏版
    "deep_think_llm": "deepseek-r1",
    "quick_think_llm": "deepseek-r1-distill-llama-70b",
    
    # 平衡的分析参数
    "max_debate_rounds": 2,
    "max_risk_discuss_rounds": 2,
    
    # 核心分析师团队
    "analysts": ["market", "fundamentals", "news"],
    "researchers": ["bull", "bear"],
})

# R1模型特性说明
R1_MODEL_FEATURES = {
    "deepseek-r1": {
        "核心特性": [
            "🧠 强大的推理能力",
            "🔍 多步骤逻辑分析",
            "📊 复杂数据理解",
            "🎯 精准决策支持",
            "📈 金融专业知识"
        ],
        "技术优势": [
            "长文本理解 (65K tokens)",
            "思维链推理",
            "多模态分析",
            "上下文学习",
            "零样本推理"
        ],
        "适用场景": [
            "复杂投资决策",
            "多因子分析",
            "风险评估",
            "策略制定",
            "市场预测"
        ],
        "性能指标": {
            "推理能力": "⭐⭐⭐⭐⭐",
            "分析深度": "⭐⭐⭐⭐⭐",
            "响应速度": "⭐⭐⭐",
            "成本效益": "⭐⭐⭐",
            "准确性": "⭐⭐⭐⭐⭐"
        }
    },
    "deepseek-r1-distill-llama-70b": {
        "核心特性": [
            "⚡ 快速推理",
            "💰 成本优化",
            "🔄 高效处理",
            "📱 实时响应",
            "🎯 实用性强"
        ],
        "技术优势": [
            "基于Llama架构",
            "知识蒸馏优化",
            "高效推理引擎",
            "低延迟响应",
            "资源友好"
        ],
        "适用场景": [
            "实时交易分析",
            "快速决策支持",
            "批量数据处理",
            "日常投资咨询",
            "自动化分析"
        ],
        "性能指标": {
            "推理能力": "⭐⭐⭐⭐",
            "分析深度": "⭐⭐⭐⭐",
            "响应速度": "⭐⭐⭐⭐⭐",
            "成本效益": "⭐⭐⭐⭐⭐",
            "准确性": "⭐⭐⭐⭐"
        }
    }
}

# 配置选择指南
CONFIG_SELECTION_GUIDE = {
    "场景": {
        "专业机构投资": "DEEPSEEK_R1_PRO_CONFIG",
        "个人投资理财": "DEEPSEEK_R1_CONFIG", 
        "日内交易": "DEEPSEEK_R1_FAST_CONFIG",
        "量化研究": "DEEPSEEK_R1_PRO_CONFIG",
        "投资咨询": "DEEPSEEK_R1_HYBRID_CONFIG"
    },
    "预算考虑": {
        "高预算高质量": "DEEPSEEK_R1_PRO_CONFIG",
        "中等预算平衡": "DEEPSEEK_R1_HYBRID_CONFIG",
        "低预算高效": "DEEPSEEK_R1_FAST_CONFIG"
    },
    "时间要求": {
        "深度分析不急": "DEEPSEEK_R1_PRO_CONFIG",
        "平衡分析适中": "DEEPSEEK_R1_CONFIG",
        "快速分析实时": "DEEPSEEK_R1_FAST_CONFIG"
    }
}

def get_r1_config(config_type: str = "default"):
    """
    获取 DeepSeek R1 配置
    
    Args:
        config_type: 配置类型
            - "default": 基础配置
            - "pro": 高性能配置  
            - "fast": 快速配置
            - "hybrid": 混合配置
    
    Returns:
        dict: 配置字典
    """
    config_map = {
        "default": DEEPSEEK_R1_CONFIG,
        "pro": DEEPSEEK_R1_PRO_CONFIG,
        "fast": DEEPSEEK_R1_FAST_CONFIG,
        "hybrid": DEEPSEEK_R1_HYBRID_CONFIG
    }
    
    return config_map.get(config_type, DEEPSEEK_R1_CONFIG)

def print_r1_features():
    """打印R1模型特性"""
    print("🧠 DeepSeek R1 模型特性:")
    print("=" * 60)
    
    for model, features in R1_MODEL_FEATURES.items():
        print(f"\n📊 {model}:")
        print(f"  核心特性: {', '.join(features['核心特性'])}")
        print(f"  技术优势: {', '.join(features['技术优势'])}")
        print(f"  适用场景: {', '.join(features['适用场景'])}")
        print("  性能指标:")
        for metric, rating in features['性能指标'].items():
            print(f"    {metric}: {rating}")

def print_config_guide():
    """打印配置选择指南"""
    print("\n💡 配置选择指南:")
    print("=" * 60)
    
    for category, options in CONFIG_SELECTION_GUIDE.items():
        print(f"\n📋 {category}:")
        for scenario, config in options.items():
            print(f"  {scenario}: {config}")

if __name__ == "__main__":
    print_r1_features()
    print_config_guide()
