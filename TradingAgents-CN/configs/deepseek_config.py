"""
DeepSeek 大模型配置示例
适用于使用 DeepSeek 进行股票分析的场景
"""

import os
from tradingagents.default_config import DEFAULT_CONFIG

# DeepSeek 配置
DEEPSEEK_CONFIG = DEFAULT_CONFIG.copy()
DEEPSEEK_CONFIG.update({
    # LLM 提供商设置
    "llm_provider": "deepseek",
    "backend_url": "https://api.deepseek.com/v1",
    
    # DeepSeek 模型配置
    "deep_think_llm": "deepseek-chat",      # 深度思考模型
    "quick_think_llm": "deepseek-chat",     # 快速思考模型
    
    # 分析参数优化
    "max_debate_rounds": 2,                 # 增加辩论轮数，提高分析质量
    "max_risk_discuss_rounds": 2,           # 增加风险讨论轮数
    
    # 工具设置
    "online_tools": True,                   # 启用在线工具
    "memory_enabled": True,                 # 启用记忆功能
})

# DeepSeek 专业金融分析配置
DEEPSEEK_FINANCE_CONFIG = DEEPSEEK_CONFIG.copy()
DEEPSEEK_FINANCE_CONFIG.update({
    # 使用数学专业模型进行量化分析
    "deep_think_llm": "deepseek-math",      # 数学模型，适合量化分析
    "quick_think_llm": "deepseek-chat",     # 通用模型，适合文本分析
    
    # 增强分析深度
    "max_debate_rounds": 3,
    "max_risk_discuss_rounds": 3,
    
    # 专业分析师配置
    "analysts": ["market", "fundamentals", "news", "social"],
    "researchers": ["bull", "bear"],
})

# DeepSeek 快速分析配置（适合日内交易）
DEEPSEEK_FAST_CONFIG = DEEPSEEK_CONFIG.copy()
DEEPSEEK_FAST_CONFIG.update({
    # 使用相同模型减少切换开销
    "deep_think_llm": "deepseek-chat",
    "quick_think_llm": "deepseek-chat",
    
    # 减少分析轮数，提高速度
    "max_debate_rounds": 1,
    "max_risk_discuss_rounds": 1,
    
    # 精简分析师团队
    "analysts": ["market", "news"],
    "researchers": ["bull"],
})

# 模型性能对比
DEEPSEEK_MODEL_COMPARISON = {
    "deepseek-chat": {
        "优势": ["通用性强", "中文理解好", "推理能力强"],
        "适用场景": ["综合分析", "文本理解", "投资建议"],
        "成本": "中等",
        "速度": "快"
    },
    "deepseek-coder": {
        "优势": ["代码能力强", "逻辑推理好", "技术分析准确"],
        "适用场景": ["技术指标分析", "量化策略", "数据处理"],
        "成本": "中等",
        "速度": "快"
    },
    "deepseek-math": {
        "优势": ["数学计算准确", "统计分析强", "量化建模好"],
        "适用场景": ["财务分析", "风险计算", "量化投资"],
        "成本": "低",
        "速度": "中等"
    }
}

# 使用建议
USAGE_RECOMMENDATIONS = {
    "新手投资者": {
        "推荐配置": "DEEPSEEK_CONFIG",
        "推荐模型": "deepseek-chat",
        "原因": "通用性强，分析全面，易于理解"
    },
    "专业投资者": {
        "推荐配置": "DEEPSEEK_FINANCE_CONFIG", 
        "推荐模型": "deepseek-math + deepseek-chat",
        "原因": "专业分析深度，量化能力强"
    },
    "日内交易者": {
        "推荐配置": "DEEPSEEK_FAST_CONFIG",
        "推荐模型": "deepseek-chat",
        "原因": "分析速度快，决策及时"
    },
    "量化研究": {
        "推荐配置": "DEEPSEEK_FINANCE_CONFIG",
        "推荐模型": "deepseek-math",
        "原因": "数学建模能力强，计算准确"
    }
}

def get_deepseek_config(config_type: str = "default"):
    """
    获取 DeepSeek 配置
    
    Args:
        config_type: 配置类型 ("default", "finance", "fast")
    
    Returns:
        dict: 配置字典
    """
    if config_type == "finance":
        return DEEPSEEK_FINANCE_CONFIG
    elif config_type == "fast":
        return DEEPSEEK_FAST_CONFIG
    else:
        return DEEPSEEK_CONFIG

def print_model_comparison():
    """打印模型对比信息"""
    print("🧠 DeepSeek 模型对比:")
    print("=" * 60)
    
    for model, info in DEEPSEEK_MODEL_COMPARISON.items():
        print(f"\n📊 {model}:")
        print(f"  优势: {', '.join(info['优势'])}")
        print(f"  适用: {', '.join(info['适用场景'])}")
        print(f"  成本: {info['成本']} | 速度: {info['速度']}")

def print_usage_recommendations():
    """打印使用建议"""
    print("\n💡 使用建议:")
    print("=" * 60)
    
    for user_type, rec in USAGE_RECOMMENDATIONS.items():
        print(f"\n👤 {user_type}:")
        print(f"  推荐配置: {rec['推荐配置']}")
        print(f"  推荐模型: {rec['推荐模型']}")
        print(f"  原因: {rec['原因']}")

if __name__ == "__main__":
    print_model_comparison()
    print_usage_recommendations()
