#!/usr/bin/env python3
"""
DeepSeek 大模型使用示例
演示如何使用 DeepSeek 进行股票分析
"""

import os
import sys
from datetime import date

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tradingagents.graph.trading_graph import TradingGraph
from configs.deepseek_config import get_deepseek_config, print_model_comparison, print_usage_recommendations


def test_deepseek_connection():
    """测试 DeepSeek API 连接"""
    print("🧠 测试 DeepSeek API 连接...")
    
    try:
        from tradingagents.llm_adapters.deepseek_adapter import ChatDeepSeek
        
        # 创建 DeepSeek 实例
        llm = ChatDeepSeek(model="deepseek-chat")
        
        # 测试简单对话
        from langchain_core.messages import HumanMessage
        
        response = llm.invoke([
            HumanMessage(content="你好，请简单介绍一下你的能力。")
        ])
        
        print("✅ DeepSeek 连接成功！")
        print(f"📝 响应: {response.content[:100]}...")
        return True
        
    except Exception as e:
        print(f"❌ DeepSeek 连接失败: {e}")
        return False


def analyze_stock_with_deepseek(stock_symbol: str, config_type: str = "default"):
    """使用 DeepSeek 分析股票"""
    
    print(f"\n🎯 使用 DeepSeek 分析股票: {stock_symbol}")
    print(f"📊 配置类型: {config_type}")
    print("=" * 60)
    
    try:
        # 获取 DeepSeek 配置
        config = get_deepseek_config(config_type)
        
        # 创建交易图
        trading_graph = TradingGraph(config=config)
        
        # 执行分析
        print("🚀 开始分析...")
        state, decision = trading_graph.propagate(stock_symbol, str(date.today()))
        
        # 显示结果
        print("\n📈 分析结果:")
        print("=" * 40)
        
        if decision:
            print(f"💡 投资建议: {decision.get('action', 'N/A')}")
            print(f"🎯 置信度: {decision.get('confidence', 'N/A')}")
            print(f"⚠️ 风险评分: {decision.get('risk_score', 'N/A')}")
            
            if 'reasoning' in decision:
                print(f"\n📝 分析推理:")
                print(decision['reasoning'][:500] + "..." if len(decision['reasoning']) > 500 else decision['reasoning'])
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False


def compare_deepseek_models():
    """对比不同 DeepSeek 模型的分析结果"""
    
    print("\n🔍 DeepSeek 模型对比分析")
    print("=" * 60)
    
    models = ["deepseek-chat", "deepseek-math", "deepseek-coder"]
    stock_symbol = "000001"  # 平安银行
    
    results = {}
    
    for model in models:
        print(f"\n🧠 测试模型: {model}")
        print("-" * 30)
        
        try:
            # 创建配置
            config = get_deepseek_config("default")
            config["deep_think_llm"] = model
            config["quick_think_llm"] = model
            
            # 创建交易图
            trading_graph = TradingGraph(config=config)
            
            # 执行分析
            state, decision = trading_graph.propagate(stock_symbol, str(date.today()))
            
            results[model] = {
                "success": True,
                "decision": decision,
                "action": decision.get('action', 'N/A') if decision else 'N/A',
                "confidence": decision.get('confidence', 'N/A') if decision else 'N/A'
            }
            
            print(f"✅ {model} 分析完成")
            
        except Exception as e:
            results[model] = {
                "success": False,
                "error": str(e)
            }
            print(f"❌ {model} 分析失败: {e}")
    
    # 显示对比结果
    print("\n📊 模型对比结果:")
    print("=" * 60)
    
    for model, result in results.items():
        print(f"\n🧠 {model}:")
        if result["success"]:
            print(f"  投资建议: {result['action']}")
            print(f"  置信度: {result['confidence']}")
        else:
            print(f"  状态: 失败 - {result['error']}")


def main():
    """主函数"""
    
    print("🚀 DeepSeek 大模型集成示例")
    print("=" * 60)
    
    # 检查环境变量
    if not os.getenv("DEEPSEEK_API_KEY"):
        print("⚠️ 警告: 未设置 DEEPSEEK_API_KEY 环境变量")
        print("请在 .env 文件中配置您的 DeepSeek API 密钥")
        print("获取地址: https://platform.deepseek.com/")
        return
    
    # 显示模型信息
    print_model_comparison()
    print_usage_recommendations()
    
    # 测试连接
    if not test_deepseek_connection():
        return
    
    # 示例1: 基础分析
    print("\n" + "="*60)
    print("📊 示例1: 基础股票分析")
    analyze_stock_with_deepseek("000001", "default")
    
    # 示例2: 专业金融分析
    print("\n" + "="*60)
    print("📊 示例2: 专业金融分析")
    analyze_stock_with_deepseek("600519", "finance")  # 贵州茅台
    
    # 示例3: 快速分析
    print("\n" + "="*60)
    print("📊 示例3: 快速分析")
    analyze_stock_with_deepseek("300750", "fast")  # 宁德时代
    
    # 示例4: 模型对比
    print("\n" + "="*60)
    print("📊 示例4: 模型对比分析")
    compare_deepseek_models()
    
    print("\n✅ 所有示例执行完成！")


if __name__ == "__main__":
    main()
