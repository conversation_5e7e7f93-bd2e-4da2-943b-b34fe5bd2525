# 上游同步配置文件

# 基本配置
upstream:
  repository: "TauricResearch/TradingAgents"
  url: "https://github.com/TauricResearch/TradingAgents.git"
  branch: "main"

origin:
  repository: "hsliuping/TradingAgents-CN"
  url: "https://github.com/hsliuping/TradingAgents-CN.git"
  branch: "main"

# 同步策略配置
sync_strategy:
  # 默认合并策略: merge, rebase, cherry-pick
  default_method: "merge"
  
  # 自动同步条件
  auto_sync:
    enabled: true
    conditions:
      # 只有文档更新时自动同步
      docs_only: true
      # 没有Bug修复时自动同步
      no_fixes: true
      # 提交数量少于此值时自动同步
      max_commits: 3
  
  # 冲突处理策略
  conflict_resolution:
    # 文档冲突：保持我们的版本
    docs:
      strategy: "ours"
      files:
        - "README.md"
        - "docs/**"
        - "*.md"
    
    # 配置文件：手动处理
    config:
      strategy: "manual"
      files:
        - "config/**"
        - "*.yaml"
        - "*.json"
        - "requirements.txt"
    
    # 核心代码：优先上游版本
    core:
      strategy: "theirs"
      files:
        - "tradingagents/**"
        - "src/**"
    
    # 测试文件：合并处理
    tests:
      strategy: "merge"
      files:
        - "tests/**"
        - "test_**"

# 文件处理规则
file_rules:
  # 保护的文件（不会被上游覆盖）
  protected_files:
    - "README.md"
    - "README-CN.md"
    - "CONTRIBUTING.md"
    - "CHANGELOG.md"
    - "PROJECT_INFO.md"
    - "docs/**"
    - "scripts/sync_upstream.py"
    - "sync_config.yaml"
    - ".github/workflows/upstream-sync-check.yml"
  
  # 忽略的文件（不参与同步）
  ignored_files:
    - ".git/**"
    - "sync_reports/**"
    - "*.log"
    - "__pycache__/**"
    - "*.pyc"
  
  # 需要特殊处理的文件
  special_handling:
    "requirements.txt":
      action: "merge_dependencies"
      description: "合并依赖包，保留我们的额外依赖"
    
    "tradingagents/default_config.py":
      action: "merge_config"
      description: "合并配置，保留中文相关配置"

# 通知配置
notifications:
  # GitHub Issue通知
  github_issues:
    enabled: true
    labels:
      - "upstream-sync"
    assignees:
      - "hsliuping"
  
  # 邮件通知（如果配置了）
  email:
    enabled: false
    recipients:
      - "<EMAIL>"
    
  # Webhook通知（如果需要）
  webhook:
    enabled: false
    url: ""

# 测试配置
testing:
  # 同步后自动运行的测试
  post_sync_tests:
    - "python -m pytest tests/test_basic.py"
    - "python examples/basic_example.py"
  
  # 测试失败时的处理
  on_test_failure:
    action: "create_issue"
    rollback: false

# 报告配置
reporting:
  # 报告存储目录
  reports_dir: "sync_reports"
  
  # 报告格式
  format: "json"
  
  # 保留报告数量
  max_reports: 50
  
  # 报告内容
  include:
    - "commit_details"
    - "conflict_resolution"
    - "test_results"
    - "file_changes"

# 高级配置
advanced:
  # 同步前备份
  backup:
    enabled: true
    method: "tag"  # tag, branch, archive
    prefix: "backup-"
  
  # 并行处理
  parallel:
    enabled: false
    max_workers: 2
  
  # 缓存配置
  cache:
    enabled: true
    ttl: 3600  # 1小时
  
  # 重试配置
  retry:
    max_attempts: 3
    delay: 5  # 秒
    backoff: 2  # 指数退避因子

# 自定义钩子
hooks:
  # 同步前执行
  pre_sync:
    - "echo '开始同步前检查...'"
    - "git status --porcelain"
  
  # 同步后执行
  post_sync:
    - "echo '同步完成，运行测试...'"
    - "python -m pytest tests/ --tb=short"
  
  # 冲突解决后执行
  post_conflict_resolution:
    - "echo '冲突已解决，验证代码...'"
    - "python -c 'import tradingagents; print(\"导入成功\")'"

# 版本管理
versioning:
  # 自动版本标记
  auto_tag:
    enabled: true
    pattern: "v{version}-cn-sync-{date}"
    
  # 版本号策略
  version_strategy:
    # 跟随上游版本
    follow_upstream: true
    # 添加中文版本后缀
    cn_suffix: true
    # 版本格式
    format: "{upstream_version}-cn.{build_number}"

# 社区协作
community:
  # 向上游贡献
  contribute_upstream:
    enabled: true
    # 自动创建PR的条件
    auto_pr_conditions:
      - "bug_fixes"
      - "performance_improvements"
    
  # 问题反馈
  issue_feedback:
    enabled: true
    # 自动报告发现的问题
    auto_report: true
