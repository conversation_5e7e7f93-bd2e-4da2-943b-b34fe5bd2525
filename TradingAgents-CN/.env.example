# TradingAgents-CN 环境变量配置示例
# 🔐 重要：复制此文件为 .env 并填入您的真实API密钥
# ⚠️  警告：.env文件包含敏感信息，请勿提交到Git仓库

# ===== 必需的API密钥 =====

# 🇨🇳 阿里百炼 API 密钥 (推荐，国产大模型，中文优化)
# 获取地址: https://dashscope.aliyun.com/
# 注册阿里云账号 -> 开通百炼服务 -> 获取API密钥
# 格式: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
DASHSCOPE_API_KEY=your_dashscope_api_key_here

# 📊 FinnHub API 密钥 (可选，用于获取金融数据)
# 获取地址: https://finnhub.io/
# 免费账户每分钟60次请求，足够日常使用
# 格式: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
FINNHUB_API_KEY=your_finnhub_api_key_here

# ===== 可选的API密钥 =====

# 📰 实时新闻API密钥 (可选，用于减少新闻滞后性)
# Alpha Vantage API 密钥 (提供实时新闻和情绪分析)
# 获取地址: https://www.alphavantage.co/support/#api-key
# 免费账户每天500次请求
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key_here

# NewsAPI 密钥 (提供全球新闻覆盖)
# 获取地址: https://newsapi.org/
# 免费账户每天1000次请求
NEWSAPI_KEY=your_newsapi_key_here

# 🌍 OpenAI API 密钥 (可选，需要国外网络)
# 获取地址: https://platform.openai.com/
# 格式: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
OPENAI_API_KEY=your_openai_api_key_here

# 🔍 Google AI API 密钥 (可选，用于Gemini模型)
# 获取地址: https://ai.google.dev/
GOOGLE_API_KEY=your_google_api_key_here

# 🤖 Anthropic API 密钥 (可选，用于Claude模型)
# 获取地址: https://console.anthropic.com/
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# ===== 项目配置 =====

# 结果存储目录
TRADINGAGENTS_RESULTS_DIR=./results

# 日志级别 (DEBUG, INFO, WARNING, ERROR)
TRADINGAGENTS_LOG_LEVEL=INFO

# ===== Reddit API 配置 (可选) =====
# 用于获取社交媒体情绪数据
# 获取地址: https://www.reddit.com/prefs/apps

# Reddit 客户端ID
REDDIT_CLIENT_ID=your_reddit_client_id

# Reddit 客户端密钥
REDDIT_CLIENT_SECRET=your_reddit_client_secret

# Reddit 用户代理
REDDIT_USER_AGENT=TradingAgents-CN/1.0

# ===== 使用说明 =====
# 1. 复制此文件为 .env: cp .env.example .env
# 2. 编辑 .env 文件，填入您的真实API密钥
# 3. 至少需要配置 DASHSCOPE_API_KEY 和 FINNHUB_API_KEY
# 4. 运行 python -m cli.main config 检查配置状态
# 5. 运行 python -m cli.main test 验证配置是否正确
