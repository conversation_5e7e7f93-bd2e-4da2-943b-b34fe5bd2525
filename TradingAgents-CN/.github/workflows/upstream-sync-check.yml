name: 上游同步检查

on:
  schedule:
    # 每周一上午9点检查上游更新
    - cron: '0 9 * * 1'
  workflow_dispatch:
    # 允许手动触发
    inputs:
      force_sync:
        description: '强制同步（跳过确认）'
        required: false
        default: 'false'
        type: boolean

jobs:
  check-upstream:
    runs-on: ubuntu-latest
    name: 检查上游更新
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        token: ${{ secrets.GITHUB_TOKEN }}
    
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install requests
    
    - name: 配置Git
      run: |
        git config --global user.name 'GitHub Actions'
        git config --global user.email '<EMAIL>'
    
    - name: 添加上游仓库
      run: |
        git remote add upstream https://github.com/TauricResearch/TradingAgents.git || true
        git fetch upstream
    
    - name: 检查上游更新
      id: check_updates
      run: |
        # 获取上游新提交数量
        NEW_COMMITS=$(git rev-list --count HEAD..upstream/main)
        echo "new_commits=$NEW_COMMITS" >> $GITHUB_OUTPUT
        
        if [ "$NEW_COMMITS" -gt 0 ]; then
          echo "has_updates=true" >> $GITHUB_OUTPUT
          echo "发现 $NEW_COMMITS 个新提交"
          
          # 获取最新提交信息
          git log --oneline --no-merges HEAD..upstream/main | head -10 > recent_commits.txt
          echo "recent_commits<<EOF" >> $GITHUB_OUTPUT
          cat recent_commits.txt >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT
        else
          echo "has_updates=false" >> $GITHUB_OUTPUT
          echo "没有新的上游更新"
        fi
    
    - name: 分析更新类型
      if: steps.check_updates.outputs.has_updates == 'true'
      id: analyze_updates
      run: |
        # 分析提交类型
        FEATURES=$(git log --oneline --no-merges HEAD..upstream/main | grep -i -E "(feat|feature|add)" | wc -l)
        FIXES=$(git log --oneline --no-merges HEAD..upstream/main | grep -i -E "(fix|bug|patch)" | wc -l)
        DOCS=$(git log --oneline --no-merges HEAD..upstream/main | grep -i -E "(doc|readme)" | wc -l)
        
        echo "features=$FEATURES" >> $GITHUB_OUTPUT
        echo "fixes=$FIXES" >> $GITHUB_OUTPUT
        echo "docs=$DOCS" >> $GITHUB_OUTPUT
        
        # 判断更新优先级
        if [ "$FIXES" -gt 0 ]; then
          echo "priority=high" >> $GITHUB_OUTPUT
          echo "reason=包含Bug修复" >> $GITHUB_OUTPUT
        elif [ "$FEATURES" -gt 2 ]; then
          echo "priority=medium" >> $GITHUB_OUTPUT
          echo "reason=包含多个新功能" >> $GITHUB_OUTPUT
        else
          echo "priority=low" >> $GITHUB_OUTPUT
          echo "reason=常规更新" >> $GITHUB_OUTPUT
        fi
    
    - name: 创建Issue报告
      if: steps.check_updates.outputs.has_updates == 'true'
      uses: actions/github-script@v7
      with:
        script: |
          const newCommits = '${{ steps.check_updates.outputs.new_commits }}';
          const recentCommits = `${{ steps.check_updates.outputs.recent_commits }}`;
          const features = '${{ steps.analyze_updates.outputs.features }}';
          const fixes = '${{ steps.analyze_updates.outputs.fixes }}';
          const docs = '${{ steps.analyze_updates.outputs.docs }}';
          const priority = '${{ steps.analyze_updates.outputs.priority }}';
          const reason = '${{ steps.analyze_updates.outputs.reason }}';
          
          const issueTitle = `🔄 上游更新检测 - ${newCommits} 个新提交`;
          const issueBody = `
          ## 📊 更新概览
          
          - **新提交数量**: ${newCommits}
          - **更新优先级**: ${priority.toUpperCase()}
          - **优先级原因**: ${reason}
          
          ## 📈 更新分析
          
          - 🆕 新功能: ${features} 个
          - 🐛 Bug修复: ${fixes} 个  
          - 📚 文档更新: ${docs} 个
          
          ## 📋 最近提交
          
          \`\`\`
          ${recentCommits}
          \`\`\`
          
          ## 🎯 建议行动
          
          ${priority === 'high' ? 
            '⚠️ **建议立即同步** - 包含重要的Bug修复' : 
            priority === 'medium' ? 
            '📅 **建议本周内同步** - 包含有价值的新功能' : 
            '📝 **可以计划同步** - 常规更新，可以安排时间同步'
          }
          
          ## 🔧 同步步骤
          
          1. 检查当前工作状态
          2. 运行同步脚本: \`python scripts/sync_upstream.py\`
          3. 解决可能的冲突
          4. 测试功能完整性
          5. 更新相关文档
          
          ## 📞 相关链接
          
          - [上游仓库](https://github.com/TauricResearch/TradingAgents)
          - [同步策略文档](docs/maintenance/upstream-sync.md)
          - [同步脚本](scripts/sync_upstream.py)
          
          ---
          
          *此Issue由GitHub Actions自动创建于 ${new Date().toISOString()}*
          `;
          
          // 检查是否已有相似的Issue
          const existingIssues = await github.rest.issues.listForRepo({
            owner: context.repo.owner,
            repo: context.repo.repo,
            state: 'open',
            labels: 'upstream-sync'
          });
          
          const hasOpenSyncIssue = existingIssues.data.some(issue => 
            issue.title.includes('上游更新检测')
          );
          
          if (!hasOpenSyncIssue) {
            await github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: issueTitle,
              body: issueBody,
              labels: ['upstream-sync', priority === 'high' ? 'priority-high' : priority === 'medium' ? 'priority-medium' : 'priority-low']
            });
            
            console.log('✅ 已创建上游更新Issue');
          } else {
            console.log('ℹ️ 已存在开放的同步Issue，跳过创建');
          }
    
    - name: 发送通知
      if: steps.check_updates.outputs.has_updates == 'true'
      run: |
        echo "📧 上游更新通知已发送"
        echo "- 新提交数量: ${{ steps.check_updates.outputs.new_commits }}"
        echo "- 更新优先级: ${{ steps.analyze_updates.outputs.priority }}"
        echo "- 已创建Issue进行跟踪"

  auto-sync:
    runs-on: ubuntu-latest
    name: 自动同步（仅限低风险更新）
    needs: check-upstream
    if: github.event.inputs.force_sync == 'true' || (needs.check-upstream.outputs.priority == 'low' && needs.check-upstream.outputs.fixes == '0')
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        token: ${{ secrets.GITHUB_TOKEN }}
    
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: 配置Git
      run: |
        git config --global user.name 'GitHub Actions Bot'
        git config --global user.email '<EMAIL>'
    
    - name: 添加上游仓库
      run: |
        git remote add upstream https://github.com/TauricResearch/TradingAgents.git
        git fetch upstream
    
    - name: 执行自动同步
      run: |
        python scripts/sync_upstream.py --auto
    
    - name: 推送更新
      run: |
        git push origin main
    
    - name: 创建同步报告
      uses: actions/github-script@v7
      with:
        script: |
          const reportTitle = '🤖 自动同步完成';
          const reportBody = `
          ## ✅ 自动同步成功
          
          GitHub Actions 已自动完成上游同步。
          
          **同步时间**: ${new Date().toISOString()}
          **触发方式**: ${context.eventName === 'workflow_dispatch' ? '手动触发' : '自动触发'}
          
          ## 📋 后续建议
          
          1. 检查同步的更改是否正常
          2. 运行本地测试验证功能
          3. 更新相关文档（如需要）
          
          ---
          
          *此报告由GitHub Actions自动生成*
          `;
          
          await github.rest.issues.create({
            owner: context.repo.owner,
            repo: context.repo.repo,
            title: reportTitle,
            body: reportBody,
            labels: ['upstream-sync', 'auto-sync']
          });
