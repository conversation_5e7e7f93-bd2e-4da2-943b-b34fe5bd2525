# 更新日志 | Changelog

本文档记录了 TradingAgents 中文增强版的所有重要更改。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [cn-0.1.3] - 2025-06-28

### 🇨🇳 A股市场完整支持 | Complete A-Share Market Support

#### ✨ 新增功能 | Added Features

**🏛️ 通达信API集成**
- 完整的A股实时行情数据支持
- 历史K线数据和技术指标计算
- 10个验证可用的服务器，自动故障切换
- 支持深圳主板、中小板、创业板、上海主板、科创板

**🌐 Web界面市场选择**
- 新增美股/A股市场切换功能
- 智能股票代码格式提示
- 支持A股代码格式：000001, 600519, 300750等
- 优化的用户体验和界面响应

**🤖 中国市场分析师**
- 专门的A股市场分析智能体
- A股特色分析：涨跌停、ST股票、板块轮动
- 中国政策对股价影响的专业分析
- 本土化的技术指标和分析方法

**📰 实时新闻优化**
- 新闻滞后性从1-6小时降低到15-30分钟
- 多源新闻聚合：FinnHub + Alpha Vantage + NewsAPI
- 新闻紧急度自动评估
- 中文财经新闻源集成

#### 🔧 技术改进 | Technical Improvements

**📊 数据处理增强**
- A股和美股数据格式统一处理
- 增强的数据完整性和准确性验证
- 优化的数据缓存策略
- 并行服务器测试和选择

**🛡️ 错误处理改进**
- 更健壮的异常处理和错误恢复
- 详细的错误日志和调试信息
- 内置的连接和数据获取诊断工具
- 自动重连和备用服务器切换

#### 📚 文档更新 | Documentation Updates

- **A股分析使用指南**: 完整的A股使用教程
- **通达信API集成指南**: 技术实现详解
- **快速开始指南更新**: 包含A股支持的完整说明
- **发布说明**: 详细的v0.1.3功能介绍

#### 🐛 问题修复 | Bug Fixes

- 修复新闻分析的滞后性问题
- 优化社交媒体分析师的中文输出
- 改进Web界面的显示和响应问题
- 修复数据获取的稳定性问题

## [cn-0.1.2] - 2025-06-27

### 🌐 Web管理界面和Google AI支持 | Web Interface & Google AI Support

#### ✨ 新增功能 | Added Features

**🌐 Streamlit Web管理界面**

- 完整的Web股票分析平台
- 直观的用户界面和实时进度显示
- 支持多种分析师组合选择
- 可视化的分析结果展示
- 响应式设计，支持移动端访问

**🤖 Google AI模型集成**

- 完整的Google Gemini模型支持
- 支持gemini-2.0-flash、gemini-1.5-pro等模型
- 智能混合嵌入服务（Google AI + 阿里百炼）
- 完美的中文分析能力
- 稳定的LangChain集成

**🔧 多LLM提供商支持**

- Web界面支持LLM提供商选择
- 阿里百炼和Google AI无缝切换
- 自动配置最优嵌入服务
- 统一的配置管理界面

**🧪 完整的测试体系**

- 25+个专业测试文件
- API集成测试和模型兼容性测试
- 调试和诊断工具
- 规范化的tests目录结构

#### 🔧 改进优化 | Improvements

**📊 分析结果展示**

- 新增分析配置信息显示
- 显示使用的LLM提供商和模型
- 展示参与的分析师信息
- 优化的结果格式化

**🗂️ 项目结构优化**

- 所有测试文件移至tests目录
- 文档统一放在docs目录
- Web相关代码集中在web目录
- 更清晰的目录组织结构

**🔑 API密钥管理**

- 支持多种API服务配置
- 智能的API密钥检测
- 详细的配置指南
- 安全的环境变量管理

#### 🐛 问题修复 | Bug Fixes

- 修复了Gemini 2.5模型的LangChain集成问题
- 解决了Web界面模型选择的配置问题
- 优化了内存系统的稳定性
- 改进了错误处理和用户反馈

#### 📚 文档更新 | Documentation

- 完整的Web界面使用指南
- Google AI配置教程
- 测试文件分类和使用说明
- API密钥配置安全指南

## [cn-0.1.1] - 2025-06-26

### 🎉 首个中文增强预览版本 | First Chinese Enhanced Preview Release

> 📝 **版本说明**: 为避免与源项目版本冲突，中文增强版使用 `cn-` 前缀的独立版本体系

#### ✨ 新增功能 | Added Features

**🇨🇳 阿里百炼大模型集成**

- 完整的ChatDashScope适配器实现
- 支持qwen-turbo、qwen-plus、qwen-max模型
- 优化的中文理解和生成能力
- 与LangChain框架完美兼容

**🏗️ 项目结构重构**

- 创建规范的examples/目录存放演示程序
- 创建tests/目录存放测试程序
- 按功能分类组织子目录
- 添加完整的Python包结构

**🖥️ CLI工具中文化**

- 完整的中文用户界面
- 双语命令说明和帮助信息
- 中文错误提示和用户引导
- 新增config、version、examples、test、help命令

**📚 示例程序**

- demo_dashscope_chinese.py: 中文优化的股票分析演示
- demo_dashscope.py: 完整功能演示
- demo_dashscope_simple.py: 简化测试版本
- demo_dashscope_no_memory.py: 无记忆版本
- demo_openai.py: OpenAI模型演示

**🧪 测试系统**

- 集成测试框架
- 阿里百炼连接测试
- LangChain适配器测试
- 自动化测试脚本

#### 📊 项目统计 | Project Statistics

- **新增文件**: 27个
- **代码行数**: +2720行
- **支持的LLM**: 阿里百炼、OpenAI、Anthropic、Google AI
- **示例程序**: 6个
- **测试覆盖**: 集成测试

#### ⚠️ 预览版本说明 | Preview Version Notes

- 这是一个早期预览版本，功能仍在完善中
- 建议在测试环境中使用
- 欢迎反馈问题和建议
- 后续版本可能包含破坏性更改

## [未发布] | Unreleased

### 计划中 | Planned

- 中国股票市场支持（A股、港股、新三板）
- 中文数据源集成（Tushare、AkShare、Wind）
- 更多国产大语言模型支持（文心一言、智谱清言）
- 中文金融术语和表达优化
- 监管合规功能（风险提示、免责声明）
- 私有化部署支持

## [0.1.0-cn] - 2025-06-26 (历史版本)

### 新增

- ✅ **完整的中文文档体系**

  - 项目概述和快速开始指南
  - 详细的安装说明文档
  - 系统架构和设计文档
  - 智能体架构详细说明
  - 数据流处理架构文档
  - LangGraph 图结构设计说明
- ✅ **智能体详细文档**

  - 分析师团队设计和实现
  - 研究员团队和辩论机制
  - 交易员智能体决策流程
  - 风险管理智能体设计
  - 管理层智能体协调机制
- ✅ **数据处理文档**

  - 支持的数据源和API集成
  - 数据获取、清洗和处理流程
  - 多层缓存策略和优化
- ✅ **配置和使用指南**

  - 详细的配置选项说明
  - LLM模型配置和优化
  - 基础使用示例（8个实用示例）
  - 高级使用示例和扩展开发
- ✅ **帮助和支持文档**

  - 常见问题解答（FAQ）
  - 故障排除指南
  - 贡献指南和开发规范
- ✅ **项目管理文件**

  - 中文版 README.md
  - 贡献指南 (CONTRIBUTING.md)
  - 更新日志 (CHANGELOG.md)
  - 文档结构说明

### 改进

- 🔄 **文档组织结构**

  - 按功能模块组织文档
  - 清晰的导航和索引
  - 丰富的代码示例和图表
- 🔄 **用户体验优化**

  - 详细的安装和配置说明
  - 从入门到高级的学习路径
  - 实用的故障排除指南

### 基于

- **原始项目**: [TauricResearch/TradingAgents](https://github.com/TauricResearch/TradingAgents)
- **许可证**: Apache 2.0
- **开发语言**: Python 3.10+
- **核心框架**: LangChain, LangGraph

## [原始版本] - TauricResearch/TradingAgents

### 核心功能

- 🤖 多智能体协作架构
- 📊 多数据源集成（FinnHub、Yahoo Finance、Reddit、Google News）
- 🧠 多LLM支持（OpenAI、Anthropic、Google AI）
- 📈 专业化分析师团队
- 🔬 结构化研究员辩论
- 💼 智能交易决策
- 🛡️ 多层风险管理
- ⚡ 高性能并行处理

### 智能体系统

- **分析师团队**: 基本面、技术面、新闻面、社交媒体分析师
- **研究员团队**: 看涨/看跌研究员辩论机制
- **交易员**: 综合决策制定
- **风险管理**: 多角度风险评估

### 技术特性

- **实时数据处理**: 支持实时市场数据分析
- **灵活配置**: 高度可定制的智能体行为
- **缓存优化**: 智能缓存减少API调用成本
- **错误处理**: 完善的错误处理和重试机制

## 版本说明

### 版本号格式

我们使用语义化版本号：`主版本号.次版本号.修订号-标识符`

- **主版本号**: 不兼容的API修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正
- **标识符**:
  - `cn`: 中文增强版标识
  - `alpha`: 内测版本
  - `beta`: 公测版本
  - `rc`: 发布候选版本

### 更新类型说明

- **新增 (Added)**: 新功能
- **改进 (Changed)**: 对现有功能的更改
- **弃用 (Deprecated)**: 即将移除的功能
- **移除 (Removed)**: 已移除的功能
- **修复 (Fixed)**: Bug修复
- **安全 (Security)**: 安全相关的修复

## 贡献者

感谢所有为 TradingAgents 中文增强版做出贡献的开发者：

### 核心团队

- [@hsliuping](https://github.com/hsliuping) - 项目发起人，文档架构师

### 贡献者

- 欢迎更多贡献者加入！

### 🙏 特别感谢与致敬

#### 向源项目开发者致敬
- **[Tauric Research](https://github.com/TauricResearch)** - 杰出的原始项目开发团队
  - 感谢您们创造了革命性的多智能体交易框架
  - 感谢您们的开源精神，让全球开发者受益
  - 感谢您们在AI金融领域的前瞻性贡献

#### 推广使命
本项目的创建是为了**更好地在中国推广TradingAgents**：
- 🇨🇳 为中文用户提供无障碍的使用体验
- 🧠 集成国产大模型，适应国内技术环境
- 📊 支持中国金融市场和数据源
- 🎓 推动AI金融技术在中国的教育和应用

#### 社区贡献者
- 所有提供反馈和建议的用户
- 中文文档翻译和改进的贡献者
- 测试和验证功能的志愿者

## 路线图

### 短期目标 (1-3个月)

- [ ]  完善现有文档的细节
- [ ]  添加更多使用示例
- [ ]  集成第一个中文数据源 (Tushare)
- [ ]  支持第一个国产LLM (文心一言)

### 中期目标 (3-6个月)

- [ ]  完整的中国市场支持
- [ ]  多个中文数据源集成
- [ ]  多个国产LLM支持
- [ ]  性能优化和稳定性改进

### 长期目标 (6-12个月)

- [ ]  企业级功能和部署支持
- [ ]  高级量化分析功能
- [ ]  实时交易系统集成
- [ ]  移动端和Web界面

## 反馈和建议

我们非常重视用户的反馈和建议：

- **GitHub Issues**: [提交问题和建议](https://github.com/hsliuping/TradingAgents-CN/issues)
- **GitHub Discussions**: [参与讨论](https://github.com/hsliuping/TradingAgents-CN/discussions)
- **邮箱**: <EMAIL>

## 版本规范 | Version Convention

### 中文增强版版本体系

为避免与源项目版本冲突，本项目采用独立的版本号体系：

**格式**: `cn-X.Y.Z`
- **cn-**: 中文增强版标识前缀
- **X.Y.Z**: 遵循[语义化版本](https://semver.org/lang/zh-CN/)规范

**版本对应关系**:
- **cn-0.x.x**: 基于源项目的中文增强预览版
- **cn-1.x.x**: 中文增强正式版
- **cn-2.x.x**: 重大功能更新版

### 语义化版本规范
- **主版本号(X)**：不兼容的API修改
- **次版本号(Y)**：向下兼容的功能性新增
- **修订号(Z)**：向下兼容的问题修正

### 版本示例
- `cn-0.1.1`: 首个中文增强预览版
- `cn-0.2.0`: 新增LLM支持的预览版
- `cn-1.0.0`: 首个正式稳定版

## 许可证

本项目基于 Apache 2.0 许可证开源，详见 [LICENSE](LICENSE) 文件。

---

**注意**: 本更新日志将持续更新，记录项目的所有重要变更。建议用户定期查看以了解最新功能和改进。
