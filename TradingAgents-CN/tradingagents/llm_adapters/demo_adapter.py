"""
演示模式LLM适配器
在没有真实API密钥的情况下提供模拟响应
"""

import os
import random
from typing import Any, Dict, List, Optional
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import BaseMessage, AIMessage, HumanMessage, SystemMessage
from langchain_core.outputs import ChatGeneration, ChatResult
from langchain_core.callbacks.manager import CallbackManagerForLLMRun, AsyncCallbackManagerForLLMRun
from pydantic import Field


class DemoLLM(BaseChatModel):
    """演示模式的LLM适配器，提供模拟响应"""
    
    model: str = Field(default="demo-model", description="演示模型名称")
    temperature: float = Field(default=0.1, description="生成温度")
    max_tokens: int = Field(default=2000, description="最大生成token数")
    
    @property
    def _llm_type(self) -> str:
        """返回LLM类型"""
        return "demo"
    
    def _generate_demo_response(self, messages: List[BaseMessage]) -> str:
        """生成演示响应"""
        # 获取最后一条用户消息
        last_message = ""
        for msg in reversed(messages):
            if isinstance(msg, HumanMessage):
                last_message = msg.content
                break
        
        # 根据消息内容生成相应的演示响应
        if "市场分析" in last_message or "技术分析" in last_message:
            return self._generate_market_analysis()
        elif "基本面" in last_message or "财务" in last_message:
            return self._generate_fundamental_analysis()
        elif "新闻" in last_message or "舆情" in last_message:
            return self._generate_news_analysis()
        elif "社交媒体" in last_message or "情绪" in last_message:
            return self._generate_social_analysis()
        elif "风险" in last_message:
            return self._generate_risk_analysis()
        elif "交易决策" in last_message or "投资建议" in last_message:
            return self._generate_trading_decision()
        else:
            return self._generate_general_response()
    
    def _generate_market_analysis(self) -> str:
        """生成市场分析演示响应"""
        return """
## 📈 市场技术分析报告

### 技术指标分析
- **RSI指标**: 当前值65.2，处于中性偏强区间
- **MACD**: 金叉形态，短期趋势向好
- **布林带**: 股价位于中轨附近，波动性适中
- **成交量**: 近期成交量放大，资金关注度提升

### 价格走势分析
- **支撑位**: 10.50元附近有较强支撑
- **阻力位**: 12.80元附近存在压力
- **趋势判断**: 短期震荡上行，中期看涨

### 技术面评分: 7.5/10 (偏乐观)

**注意**: 这是演示数据，仅供参考。
        """
    
    def _generate_fundamental_analysis(self) -> str:
        """生成基本面分析演示响应"""
        return """
## 📊 基本面分析报告

### 财务指标分析
- **市盈率(PE)**: 15.6倍，估值合理
- **市净率(PB)**: 1.8倍，略低于行业平均
- **ROE**: 12.5%，盈利能力良好
- **负债率**: 45.2%，财务结构稳健

### 业绩表现
- **营收增长**: 同比增长8.5%，稳步提升
- **净利润**: 同比增长12.3%，盈利能力增强
- **毛利率**: 35.8%，保持较高水平

### 行业地位
- 在细分领域排名前三
- 市场份额稳定增长
- 技术创新能力较强

### 基本面评分: 8.0/10 (乐观)

**注意**: 这是演示数据，仅供参考。
        """
    
    def _generate_news_analysis(self) -> str:
        """生成新闻分析演示响应"""
        return """
## 📰 新闻舆情分析报告

### 近期重要新闻
1. **政策利好**: 行业支持政策出台，长期发展前景向好
2. **业绩预告**: 公司发布业绩预增公告，市场反应积极
3. **合作协议**: 与知名企业签署战略合作协议

### 媒体关注度
- **正面报道**: 75%
- **中性报道**: 20%
- **负面报道**: 5%

### 舆情趋势
- 整体舆情偏向积极
- 投资者信心有所提升
- 机构关注度增加

### 新闻面评分: 7.8/10 (积极)

**注意**: 这是演示数据，仅供参考。
        """
    
    def _generate_social_analysis(self) -> str:
        """生成社交媒体分析演示响应"""
        return """
## 💬 社交媒体情绪分析

### 情绪指标
- **整体情绪**: 偏乐观 (65% 正面)
- **讨论热度**: 中等偏高
- **关注人群**: 以机构投资者为主

### 热门话题
1. 业绩增长预期
2. 行业发展前景
3. 技术创新能力

### 投资者情绪
- **看多比例**: 68%
- **看空比例**: 22%
- **观望比例**: 10%

### 社交媒体评分: 7.2/10 (偏积极)

**注意**: 这是演示数据，仅供参考。
        """
    
    def _generate_risk_analysis(self) -> str:
        """生成风险分析演示响应"""
        return """
## ⚠️ 风险评估报告

### 主要风险因素
1. **市场风险**: 整体市场波动可能影响股价
2. **行业风险**: 行业竞争加剧，盈利压力增加
3. **政策风险**: 监管政策变化的不确定性

### 风险等级评估
- **系统性风险**: 中等
- **非系统性风险**: 中低
- **流动性风险**: 低
- **信用风险**: 低

### 风险控制建议
- 合理控制仓位，不超过总资产的10%
- 设置止损位，控制下行风险
- 关注政策变化和行业动态

### 综合风险评分: 6.5/10 (中等风险)

**注意**: 这是演示数据，仅供参考。
        """
    
    def _generate_trading_decision(self) -> str:
        """生成交易决策演示响应"""
        actions = ["BUY", "HOLD", "SELL"]
        action = random.choice(actions)
        confidence = random.uniform(0.6, 0.9)
        risk_score = random.uniform(0.3, 0.7)
        
        return f"""
## 🎯 投资决策建议

### 推荐操作
**投资建议**: {action}
**置信度**: {confidence:.1%}
**风险评分**: {risk_score:.1%}

### 决策依据
基于技术面、基本面、新闻面和社交媒体情绪的综合分析，
当前股票表现出以下特征：

1. 技术指标显示短期趋势向好
2. 基本面数据支撑长期价值
3. 市场情绪整体偏向积极
4. 风险因素在可控范围内

### 操作建议
- 建议分批建仓，控制风险
- 设置合理的止盈止损位
- 密切关注市场变化

**注意**: 这是演示数据，仅供参考，不构成投资建议。
        """
    
    def _generate_general_response(self) -> str:
        """生成通用演示响应"""
        return """
感谢您使用TradingAgents-CN演示模式！

当前系统正在演示模式下运行，所有分析结果均为模拟数据，仅用于展示系统功能。

要获得真实的分析结果，请：
1. 配置有效的阿里百炼API密钥
2. 或配置其他支持的LLM提供商

如需帮助，请参考项目文档。
        """
    
    def _generate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> ChatResult:
        """生成演示响应"""
        
        # 生成演示内容
        demo_content = self._generate_demo_response(messages)
        
        # 创建AI消息
        ai_message = AIMessage(content=demo_content)
        
        # 创建生成结果
        generation = ChatGeneration(message=ai_message)
        
        return ChatResult(generations=[generation])
    
    async def _agenerate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[AsyncCallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> ChatResult:
        """异步生成演示响应"""
        return self._generate(messages, stop, run_manager, **kwargs)
    
    @property
    def _identifying_params(self) -> Dict[str, Any]:
        """返回标识参数"""
        return {
            "model": self.model,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
        }


def create_demo_llm(model: str = "demo-model", **kwargs) -> DemoLLM:
    """创建演示模式LLM实例"""
    return DemoLLM(model=model, **kwargs)
