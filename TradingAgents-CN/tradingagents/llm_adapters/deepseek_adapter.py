"""
DeepSeek大模型适配器
为 TradingAgents 提供 DeepSeek 大模型的 LangChain 兼容接口
"""

import os
import json
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator, Sequence
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import BaseMessage, AIMessage, HumanMessage, SystemMessage
from langchain_core.outputs import ChatGeneration, ChatResult
from langchain_core.callbacks.manager import CallbackManagerForLLMRun, AsyncCallbackManagerForLLMRun
from langchain_core.tools import BaseTool
from langchain_core.utils.function_calling import convert_to_openai_tool
from pydantic import Field, SecretStr
import requests


class ChatDeepSeek(BaseChatModel):
    """DeepSeek大模型的 LangChain 适配器"""
    
    # 模型配置
    model: str = Field(default="deepseek-chat", description="DeepSeek 模型名称")
    api_key: Optional[SecretStr] = Field(default=None, description="DeepSeek API 密钥")
    base_url: str = Field(default="https://api.deepseek.com/v1", description="DeepSeek API 基础URL")
    temperature: float = Field(default=0.1, description="生成温度")
    max_tokens: int = Field(default=2000, description="最大生成token数")
    top_p: float = Field(default=0.9, description="核采样参数")
    
    def __init__(self, **kwargs):
        """初始化 DeepSeek 客户端"""
        super().__init__(**kwargs)
        
        # 设置API密钥
        api_key = self.api_key
        if api_key is None:
            api_key = os.getenv("DEEPSEEK_API_KEY")
        
        if api_key is None:
            raise ValueError(
                "DeepSeek API key not found. Please set DEEPSEEK_API_KEY environment variable "
                "or pass api_key parameter."
            )
        
        # 保存API密钥
        if isinstance(api_key, SecretStr):
            self._api_key = api_key.get_secret_value()
        else:
            self._api_key = api_key
    
    @property
    def _llm_type(self) -> str:
        """返回LLM类型"""
        return "deepseek"
    
    def _convert_messages_to_openai_format(self, messages: List[BaseMessage]) -> List[Dict[str, str]]:
        """将 LangChain 消息格式转换为 OpenAI 兼容格式"""
        openai_messages = []
        
        for message in messages:
            if isinstance(message, SystemMessage):
                role = "system"
            elif isinstance(message, HumanMessage):
                role = "user"
            elif isinstance(message, AIMessage):
                role = "assistant"
            else:
                # 默认作为用户消息处理
                role = "user"
            
            content = message.content
            if isinstance(content, list):
                # 处理多模态内容，目前只提取文本
                text_content = ""
                for item in content:
                    if isinstance(item, dict) and item.get("type") == "text":
                        text_content += item.get("text", "")
                content = text_content
            
            openai_messages.append({
                "role": role,
                "content": str(content)
            })
        
        return openai_messages
    
    def _generate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> ChatResult:
        """生成聊天回复"""
        
        # 转换消息格式
        openai_messages = self._convert_messages_to_openai_format(messages)
        
        # 准备请求参数
        request_data = {
            "model": self.model,
            "messages": openai_messages,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "top_p": self.top_p,
            "stream": False
        }
        
        # 添加停止词
        if stop:
            request_data["stop"] = stop
        
        # 合并额外参数
        request_data.update(kwargs)
        
        # 准备请求头
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self._api_key}"
        }
        
        try:
            # 调用 DeepSeek API
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=request_data,
                timeout=60
            )
            
            if response.status_code == 200:
                # 解析响应
                response_data = response.json()
                message_content = response_data["choices"][0]["message"]["content"]
                
                # 创建 AI 消息
                ai_message = AIMessage(content=message_content)
                
                # 创建生成结果
                generation = ChatGeneration(message=ai_message)
                
                return ChatResult(generations=[generation])
            else:
                error_detail = response.text
                raise Exception(f"DeepSeek API error: {response.status_code} - {error_detail}")
                
        except requests.exceptions.RequestException as e:
            raise Exception(f"Error calling DeepSeek API: {str(e)}")
        except Exception as e:
            raise Exception(f"Error calling DeepSeek API: {str(e)}")
    
    async def _agenerate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[AsyncCallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> ChatResult:
        """异步生成聊天回复"""
        # 目前使用同步方法，后续可以实现真正的异步
        return self._generate(messages, stop, run_manager, **kwargs)
    
    def bind_tools(
        self,
        tools: Sequence[Union[Dict[str, Any], type, BaseTool]],
        **kwargs: Any,
    ) -> "ChatDeepSeek":
        """绑定工具到模型"""
        formatted_tools = []
        for tool in tools:
            if hasattr(tool, "name") and hasattr(tool, "description"):
                # 这是一个 BaseTool 实例
                formatted_tools.append({
                    "name": tool.name,
                    "description": tool.description,
                    "parameters": getattr(tool, "args_schema", {})
                })
            elif isinstance(tool, dict):
                formatted_tools.append(tool)
            else:
                # 尝试转换为 OpenAI 工具格式
                try:
                    formatted_tools.append(convert_to_openai_tool(tool))
                except Exception:
                    pass

        # 创建新实例，保存工具信息
        new_instance = self.__class__(
            model=self.model,
            api_key=self.api_key,
            base_url=self.base_url,
            temperature=self.temperature,
            max_tokens=self.max_tokens,
            top_p=self.top_p,
            **kwargs
        )
        new_instance._tools = formatted_tools
        return new_instance

    @property
    def _identifying_params(self) -> Dict[str, Any]:
        """返回标识参数"""
        return {
            "model": self.model,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "top_p": self.top_p,
            "base_url": self.base_url,
        }


# 支持的模型列表
DEEPSEEK_MODELS = {
    # DeepSeek R1 系列 (最新推理模型)
    "deepseek-reasoner": {
        "description": "DeepSeek R1 - 最新推理模型，具备强大的逻辑推理能力",
        "context_length": 65536,
        "recommended_for": ["复杂推理", "金融分析", "投资决策", "风险评估"],
        "features": ["长文本理解", "多步推理", "逻辑分析", "决策支持"]
    },
    "deepseek-r1-distill-qwen-32b": {
        "description": "DeepSeek R1 蒸馏版 - 基于Qwen架构的高效版本",
        "context_length": 32768,
        "recommended_for": ["快速推理", "实时分析", "批量处理", "成本优化"],
        "features": ["高效推理", "快速响应", "成本友好"]
    },
    # DeepSeek 经典系列
    "deepseek-chat": {
        "description": "DeepSeek Chat - 通用对话模型，适合各种任务",
        "context_length": 32768,
        "recommended_for": ["通用对话", "代码生成", "文本分析", "推理任务"]
    },
    "deepseek-coder": {
        "description": "DeepSeek Coder - 专业代码模型，擅长编程任务",
        "context_length": 16384,
        "recommended_for": ["代码生成", "代码分析", "技术文档", "算法设计"]
    },
    "deepseek-math": {
        "description": "DeepSeek Math - 数学专业模型，擅长数学推理",
        "context_length": 4096,
        "recommended_for": ["数学计算", "量化分析", "统计推理", "金融建模"]
    },
}


def get_available_models() -> Dict[str, Dict[str, Any]]:
    """获取可用的 DeepSeek 模型列表"""
    return DEEPSEEK_MODELS


def create_deepseek_llm(
    model: str = "deepseek-chat",
    api_key: Optional[str] = None,
    base_url: str = "https://api.deepseek.com/v1",
    temperature: float = 0.1,
    max_tokens: int = 2000,
    **kwargs
) -> ChatDeepSeek:
    """创建 DeepSeek LLM 实例的便捷函数"""
    
    return ChatDeepSeek(
        model=model,
        api_key=api_key,
        base_url=base_url,
        temperature=temperature,
        max_tokens=max_tokens,
        **kwargs
    )
