#!/usr/bin/env python3
"""
DeepSeek R1 简化测试
快速验证DeepSeek R1模型的基本功能
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_deepseek_r1_basic():
    """测试DeepSeek R1基本功能"""
    print("🧠 测试DeepSeek R1基本功能...")
    
    # 检查API密钥
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        print("❌ 错误: 未找到DEEPSEEK_API_KEY环境变量")
        return False
    
    print(f"🔑 API密钥: {api_key[:8]}...{api_key[-4:]}")
    
    try:
        from tradingagents.llm_adapters.deepseek_adapter import ChatDeepSeek
        from langchain_core.messages import HumanMessage
        
        # 测试DeepSeek Chat模型
        print("🚀 创建DeepSeek Chat实例...")
        llm = ChatDeepSeek(model="deepseek-chat")
        
        # 简单测试
        print("💬 发送测试消息...")
        response = llm.invoke([
            HumanMessage(content="你好，请简单介绍一下你的能力，特别是在金融分析方面的优势。")
        ])
        
        print("✅ DeepSeek R1测试成功！")
        print(f"📝 响应内容: {response.content[:300]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ DeepSeek R1测试失败: {e}")
        return False

def test_deepseek_r1_distill():
    """测试DeepSeek R1蒸馏版"""
    print("\n⚡ 测试DeepSeek R1蒸馏版...")
    
    try:
        from tradingagents.llm_adapters.deepseek_adapter import ChatDeepSeek
        from langchain_core.messages import HumanMessage
        
        # 测试Coder模型
        llm = ChatDeepSeek(model="deepseek-coder")
        
        response = llm.invoke([
            HumanMessage(content="请快速分析一下中国A股市场的当前状况。")
        ])
        
        print("✅ DeepSeek R1蒸馏版测试成功！")
        print(f"📈 响应内容: {response.content[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ DeepSeek R1蒸馏版测试失败: {e}")
        return False

def test_stock_analysis_simple():
    """简化的股票分析测试"""
    print("\n📊 测试股票分析功能...")
    
    try:
        from tradingagents.llm_adapters.deepseek_adapter import ChatDeepSeek
        from langchain_core.messages import HumanMessage
        
        llm = ChatDeepSeek(model="deepseek-chat")
        
        # 股票分析提示
        analysis_prompt = """
        请分析平安银行(000001)的投资价值，考虑以下因素：
        1. 银行业整体发展趋势
        2. 平安银行的竞争优势
        3. 当前估值水平
        4. 风险因素
        
        请给出简要的投资建议。
        """
        
        response = llm.invoke([HumanMessage(content=analysis_prompt)])
        
        print("✅ 股票分析测试成功！")
        print(f"💡 分析结果: {response.content[:400]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 股票分析测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 DeepSeek R1 简化集成测试")
    print("=" * 50)
    
    # 显示配置信息
    print("📋 当前配置:")
    print(f"  模型: DeepSeek Chat")
    print(f"  API密钥: 已配置")
    print(f"  测试项目: 基本功能、Coder模型、股票分析")
    
    # 执行测试
    tests = [
        ("DeepSeek Chat基本功能", test_deepseek_r1_basic),
        ("DeepSeek Coder模型", test_deepseek_r1_distill),
        ("股票分析功能", test_stock_analysis_simple),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"🧪 {test_name}")
        print(f"{'='*50}")
        
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 显示测试总结
    print(f"\n{'='*50}")
    print("📊 测试总结")
    print(f"{'='*50}")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！DeepSeek R1集成成功！")
        print("\n📝 下一步:")
        print("  1. 可以在Web界面中选择DeepSeek作为模型提供商")
        print("  2. 使用deepseek-r1进行深度分析")
        print("  3. 使用deepseek-r1-distill-llama-70b进行快速分析")
    else:
        print("⚠️ 部分测试失败，请检查配置和网络连接")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
