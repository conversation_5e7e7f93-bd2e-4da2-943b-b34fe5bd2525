# DeepSeek 大模型使用指南

## 🧠 DeepSeek 集成概述

TradingAgents-CN 已成功集成 DeepSeek 大模型，为您提供强大的AI驱动股票分析能力。

### ✅ 集成状态
- **API连接**: ✅ 已配置 (sk-9bf2f...7c9a)
- **模型支持**: ✅ deepseek-chat, deepseek-coder
- **功能测试**: ✅ 基本功能、股票分析全部通过

## 🚀 快速开始

### 1. 环境配置
您的DeepSeek API密钥已配置完成：
```bash
DEEPSEEK_API_KEY=***********************************
```

### 2. 启动Web界面
```bash
python -m streamlit run web/app.py --server.port 8501
```

### 3. 选择DeepSeek模型
在Web界面的配置管理中：
- 选择 "deepseek" 作为LLM提供商
- 推荐模型：deepseek-chat (通用分析)

## 📊 支持的模型

### deepseek-chat
- **特点**: 通用对话模型，中文理解优秀
- **适用**: 综合股票分析、投资建议、市场解读
- **优势**: 平衡的性能和成本，推荐日常使用

### deepseek-coder  
- **特点**: 专业代码模型，逻辑推理强
- **适用**: 技术指标分析、量化策略、数据处理
- **优势**: 结构化分析，适合技术面研究

### deepseek-math
- **特点**: 数学专业模型，计算准确
- **适用**: 财务建模、风险计算、量化投资
- **优势**: 数值计算精确，适合量化分析

## 💡 使用建议

### 新手投资者
- **推荐模型**: deepseek-chat
- **配置**: 默认配置即可
- **原因**: 分析全面，易于理解

### 专业投资者  
- **推荐模型**: deepseek-chat + deepseek-math
- **配置**: 增加分析轮数
- **原因**: 深度分析，专业决策支持

### 日内交易者
- **推荐模型**: deepseek-chat
- **配置**: 快速配置，减少分析轮数
- **原因**: 响应快速，决策及时

## 🔧 配置示例

### 基础配置
```python
config = {
    "llm_provider": "deepseek",
    "deep_think_llm": "deepseek-chat",
    "quick_think_llm": "deepseek-chat",
    "backend_url": "https://api.deepseek.com/v1"
}
```

### 专业配置
```python
config = {
    "llm_provider": "deepseek", 
    "deep_think_llm": "deepseek-chat",
    "quick_think_llm": "deepseek-coder",
    "max_debate_rounds": 3,
    "max_risk_discuss_rounds": 3
}
```

## 📈 实际测试结果

### 基本功能测试 ✅
DeepSeek模型成功响应，展现了强大的金融分析能力：
- 实时数据处理与整合
- 专业分析框架应用  
- 智能风险识别

### 股票分析测试 ✅
对平安银行(000001)的分析包含：
- 银行业整体发展趋势
- 平安银行竞争优势分析
- 估值水平评估
- 风险因素识别
- 具体投资建议

## 🎯 下一步操作

1. **Web界面使用**
   - 访问 http://localhost:8501
   - 在配置管理中选择DeepSeek
   - 开始股票分析

2. **命令行使用**
   ```bash
   python cli/main.py --stock 000001 --provider deepseek
   ```

3. **API调用**
   ```python
   from tradingagents.graph.trading_graph import TradingGraph
   
   config = {"llm_provider": "deepseek"}
   graph = TradingGraph(config=config)
   state, decision = graph.propagate("000001", "2025-06-30")
   ```

## 💰 成本优势

DeepSeek 相比其他模型提供商具有显著的成本优势：
- 高性价比的API定价
- 优秀的中文理解能力
- 专业的金融分析能力

## 🔍 故障排除

### 常见问题

1. **API密钥错误**
   - 检查.env文件中的DEEPSEEK_API_KEY
   - 确保密钥格式正确 (sk-开头)

2. **模型不存在**
   - 使用支持的模型名称：deepseek-chat, deepseek-coder, deepseek-math
   - 检查模型名称拼写

3. **网络连接问题**
   - 确保网络连接正常
   - 检查防火墙设置

### 获取帮助
- 查看项目文档
- 提交GitHub Issue
- 联系技术支持

---

🎉 **恭喜！您已成功配置DeepSeek大模型，可以开始进行专业的AI驱动股票分析了！**
