# TradingAgents 文档

欢迎来到 TradingAgents 多智能体金融交易框架的文档中心。

## 文档结构

### 📋 概览文档
- [项目概述](./overview/project-overview.md) - 项目的基本介绍和目标
- [快速开始](./overview/quick-start.md) - 快速上手指南
- [安装指南](./overview/installation.md) - 详细的安装说明

### 🏗️ 架构文档
- [系统架构](./architecture/system-architecture.md) - 整体系统架构设计
- [智能体架构](./architecture/agent-architecture.md) - 智能体设计模式
- [数据流架构](./architecture/data-flow-architecture.md) - 数据处理流程
- [图结构设计](./architecture/graph-structure.md) - LangGraph 图结构设计

### 🤖 智能体文档
- [分析师团队](./agents/analysts.md) - 各类分析师智能体详解
- [研究员团队](./agents/researchers.md) - 研究员智能体设计
- [交易员](./agents/trader.md) - 交易决策智能体
- [风险管理](./agents/risk-management.md) - 风险管理智能体
- [管理层](./agents/managers.md) - 管理层智能体

### 📊 数据处理
- [数据源集成](./data/data-sources.md) - 支持的数据源和API
- [数据处理流程](./data/data-processing.md) - 数据获取和处理
- [缓存机制](./data/caching.md) - 数据缓存策略

### ⚙️ 配置与部署
- [配置说明](./configuration/config-guide.md) - 配置文件详解
- [LLM配置](./configuration/llm-config.md) - 大语言模型配置
- [Google AI配置](./configuration/google-ai-setup.md) - Google AI (Gemini)模型配置指南 ✨
- [Web界面配置](../web/README.md) - Web管理界面使用指南 ✨
- [部署指南](./deployment/deployment-guide.md) - 生产环境部署

### 🔧 开发指南
- [开发环境搭建](./development/dev-setup.md) - 开发环境配置
- [代码结构](./development/code-structure.md) - 代码组织结构
- [扩展开发](./development/extending.md) - 如何扩展框架
- [测试指南](./development/testing.md) - 测试策略和方法

### 📚 API参考
- [核心API](./api/core-api.md) - 核心类和方法
- [智能体API](./api/agents-api.md) - 智能体接口
- [数据API](./api/data-api.md) - 数据处理接口

### 💡 示例和教程
- [基础示例](./examples/basic-examples.md) - 基本使用示例
- [高级示例](./examples/advanced-examples.md) - 高级功能示例
- [自定义智能体](./examples/custom-agents.md) - 创建自定义智能体

### ❓ 常见问题
- [FAQ](./faq/faq.md) - 常见问题解答
- [故障排除](./faq/troubleshooting.md) - 问题诊断和解决

## 贡献指南

如果您想为文档做出贡献，请参考 [贡献指南](../CONTRIBUTING.md)。

## 联系我们

- GitHub: [TauricResearch/TradingAgents](https://github.com/TauricResearch/TradingAgents)
- Discord: [TradingResearch](https://discord.com/invite/hk9PGKShPK)
- 微信: TauricResearch
- Twitter: [@TauricResearch](https://x.com/TauricResearch)
