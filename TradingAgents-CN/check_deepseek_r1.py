#!/usr/bin/env python3
"""
检查DeepSeek R1模型的实际可用性
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_api_key():
    """检查API密钥"""
    api_key = os.getenv("DEEPSEEK_API_KEY")
    print(f"🔑 API密钥: {api_key[:8]}...{api_key[-4:] if api_key else 'None'}")
    return api_key is not None

def test_available_models():
    """测试可用的模型"""
    print("\n🧪 测试可用模型...")
    
    # 常见的DeepSeek模型名称
    models_to_test = [
        "deepseek-chat",
        "deepseek-coder", 
        "deepseek-math",
        "deepseek-reasoner",
        "deepseek-r1",
        "deepseek-r1-distill-qwen-32b",
        "deepseek-r1-distill-llama-70b"
    ]
    
    working_models = []
    
    for model in models_to_test:
        print(f"\n🔍 测试模型: {model}")
        try:
            from tradingagents.llm_adapters.deepseek_adapter import ChatDeepSeek
            from langchain_core.messages import HumanMessage
            
            llm = ChatDeepSeek(model=model)
            response = llm.invoke([
                HumanMessage(content="你好，请简单回复一下。")
            ])
            
            print(f"✅ {model} - 工作正常")
            print(f"📝 响应: {response.content[:50]}...")
            working_models.append(model)
            
        except Exception as e:
            print(f"❌ {model} - 失败: {str(e)[:100]}...")
    
    return working_models

def test_r1_specific_features():
    """测试R1模型的特定功能"""
    print("\n🧠 测试R1推理功能...")
    
    # 尝试不同的R1模型名称
    r1_models = ["deepseek-reasoner", "deepseek-r1"]
    
    for model in r1_models:
        print(f"\n🔍 测试R1模型: {model}")
        try:
            from tradingagents.llm_adapters.deepseek_adapter import ChatDeepSeek
            from langchain_core.messages import HumanMessage
            
            llm = ChatDeepSeek(model=model)
            
            # 测试推理任务
            reasoning_prompt = """
            请进行多步推理分析：
            如果A股市场今天上涨2%，同时美股昨夜下跌1%，人民币汇率稳定，
            那么明天中国银行股的走势可能如何？请给出推理过程。
            """
            
            response = llm.invoke([HumanMessage(content=reasoning_prompt)])
            
            print(f"✅ {model} R1推理测试成功")
            print(f"🧠 推理结果: {response.content[:200]}...")
            return model
            
        except Exception as e:
            print(f"❌ {model} R1推理测试失败: {str(e)[:100]}...")
    
    return None

def check_integration_status():
    """检查集成状态"""
    print("\n📊 集成状态检查...")
    
    checks = []
    
    # 1. 检查适配器导入
    try:
        from tradingagents.llm_adapters.deepseek_adapter import ChatDeepSeek
        checks.append(("DeepSeek适配器导入", True))
    except Exception as e:
        checks.append(("DeepSeek适配器导入", False, str(e)))
    
    # 2. 检查交易图集成
    try:
        from tradingagents.graph.trading_graph import TradingGraph
        config = {"llm_provider": "deepseek"}
        graph = TradingGraph(config=config)
        checks.append(("交易图集成", True))
    except Exception as e:
        checks.append(("交易图集成", False, str(e)))
    
    # 3. 检查配置文件
    try:
        from tradingagents.default_config import DEFAULT_CONFIG
        is_deepseek = DEFAULT_CONFIG["llm_provider"] == "deepseek"
        checks.append(("默认配置", is_deepseek))
    except Exception as e:
        checks.append(("默认配置", False, str(e)))
    
    # 4. 检查Web界面支持
    try:
        from web.pages.config_management import render_model_config
        checks.append(("Web界面支持", True))
    except Exception as e:
        checks.append(("Web界面支持", False, str(e)))
    
    return checks

def main():
    """主检查函数"""
    print("🔍 DeepSeek R1 集成状态全面检查")
    print("=" * 60)
    
    # 1. 检查API密钥
    if not check_api_key():
        print("❌ API密钥未配置，无法进行后续测试")
        return
    
    # 2. 检查集成状态
    integration_checks = check_integration_status()
    print("\n📋 集成状态:")
    for check in integration_checks:
        name = check[0]
        status = check[1]
        if status:
            print(f"  ✅ {name}")
        else:
            error = check[2] if len(check) > 2 else "未知错误"
            print(f"  ❌ {name}: {error}")
    
    # 3. 测试可用模型
    working_models = test_available_models()
    
    # 4. 测试R1特定功能
    r1_model = test_r1_specific_features()
    
    # 5. 生成报告
    print(f"\n{'='*60}")
    print("📊 检查报告")
    print(f"{'='*60}")
    
    print(f"🔑 API密钥: 已配置")
    print(f"📦 集成状态: {sum(1 for c in integration_checks if c[1])}/{len(integration_checks)} 通过")
    print(f"🧠 可用模型: {len(working_models)} 个")
    
    if working_models:
        print("  可用模型列表:")
        for model in working_models:
            print(f"    - {model}")
    
    if r1_model:
        print(f"🚀 R1推理模型: {r1_model} (可用)")
    else:
        print("⚠️ R1推理模型: 暂不可用，使用deepseek-chat作为替代")
    
    # 6. 建议
    print(f"\n💡 使用建议:")
    if working_models:
        recommended = working_models[0]
        print(f"  推荐模型: {recommended}")
        print(f"  配置方式: 在Web界面选择DeepSeek提供商")
        print(f"  测试命令: python test_deepseek_simple.py")
    else:
        print("  请检查网络连接和API密钥配置")
    
    print(f"\n🎯 结论:")
    if len(working_models) > 0:
        print("✅ DeepSeek集成正常，可以开始使用")
    else:
        print("❌ DeepSeek集成存在问题，需要排查")

if __name__ == "__main__":
    main()
