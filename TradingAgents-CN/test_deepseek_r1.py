#!/usr/bin/env python3
"""
DeepSeek R1 模型测试脚本
验证DeepSeek R1模型的集成和功能
"""

import os
import sys
from datetime import date
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_api_connection():
    """测试DeepSeek API连接"""
    print("🔗 测试DeepSeek API连接...")
    
    try:
        from tradingagents.llm_adapters.deepseek_adapter import ChatDeepSeek
        from langchain_core.messages import HumanMessage
        
        # 创建DeepSeek R1实例
        llm = ChatDeepSeek(model="deepseek-r1")
        
        # 测试简单对话
        response = llm.invoke([
            HumanMessage(content="你好，请简单介绍一下DeepSeek R1模型的特点。")
        ])
        
        print("✅ API连接成功！")
        print(f"📝 R1模型响应: {response.content[:200]}...")
        return True
        
    except Exception as e:
        print(f"❌ API连接失败: {e}")
        return False

def test_r1_reasoning():
    """测试R1模型的推理能力"""
    print("\n🧠 测试R1模型推理能力...")
    
    try:
        from tradingagents.llm_adapters.deepseek_adapter import ChatDeepSeek
        from langchain_core.messages import HumanMessage
        
        llm = ChatDeepSeek(model="deepseek-r1")
        
        # 测试复杂推理任务
        reasoning_prompt = """
        请分析以下投资场景并给出推理过程：
        
        场景：某科技公司股票在过去一个月内：
        1. 股价上涨了15%
        2. 发布了新产品获得市场好评
        3. 但同时行业整体面临监管压力
        4. 公司财报显示营收增长但利润率下降
        
        请运用多步推理分析这只股票的投资价值。
        """
        
        response = llm.invoke([HumanMessage(content=reasoning_prompt)])
        
        print("✅ 推理测试成功！")
        print(f"📊 推理分析: {response.content[:300]}...")
        return True
        
    except Exception as e:
        print(f"❌ 推理测试失败: {e}")
        return False

def test_distill_model():
    """测试蒸馏版模型"""
    print("\n⚡ 测试蒸馏版模型...")
    
    try:
        from tradingagents.llm_adapters.deepseek_adapter import ChatDeepSeek
        from langchain_core.messages import HumanMessage
        
        llm = ChatDeepSeek(model="deepseek-r1-distill-llama-70b")
        
        response = llm.invoke([
            HumanMessage(content="请快速分析一下当前A股市场的整体趋势。")
        ])
        
        print("✅ 蒸馏版模型测试成功！")
        print(f"📈 快速分析: {response.content[:200]}...")
        return True
        
    except Exception as e:
        print(f"❌ 蒸馏版模型测试失败: {e}")
        return False

def test_stock_analysis():
    """测试完整股票分析流程"""
    print("\n📊 测试完整股票分析流程...")
    
    try:
        from tradingagents.graph.trading_graph import TradingGraph
        from configs.deepseek_r1_config import get_r1_config
        
        # 使用R1混合配置
        config = get_r1_config("hybrid")
        
        # 创建交易图
        trading_graph = TradingGraph(config=config)
        
        # 分析平安银行
        stock_symbol = "000001"
        analysis_date = str(date.today())
        
        print(f"🎯 分析股票: {stock_symbol}")
        print(f"📅 分析日期: {analysis_date}")
        
        # 执行分析
        state, decision = trading_graph.propagate(stock_symbol, analysis_date)
        
        if decision:
            print("✅ 股票分析成功！")
            print(f"💡 投资建议: {decision.get('action', 'N/A')}")
            print(f"🎯 置信度: {decision.get('confidence', 'N/A')}")
            print(f"⚠️ 风险评分: {decision.get('risk_score', 'N/A')}")
            
            if 'reasoning' in decision:
                print(f"📝 分析推理: {decision['reasoning'][:300]}...")
        else:
            print("⚠️ 分析完成但未获得决策结果")
        
        return True
        
    except Exception as e:
        print(f"❌ 股票分析失败: {e}")
        return False

def test_model_comparison():
    """测试不同模型的性能对比"""
    print("\n🔍 测试模型性能对比...")
    
    models = ["deepseek-r1", "deepseek-r1-distill-llama-70b"]
    test_prompt = "请简要分析贵州茅台(600519)的投资价值。"
    
    results = {}
    
    for model in models:
        print(f"\n🧠 测试模型: {model}")
        try:
            from tradingagents.llm_adapters.deepseek_adapter import ChatDeepSeek
            from langchain_core.messages import HumanMessage
            import time
            
            llm = ChatDeepSeek(model=model)
            
            start_time = time.time()
            response = llm.invoke([HumanMessage(content=test_prompt)])
            end_time = time.time()
            
            results[model] = {
                "success": True,
                "response_time": end_time - start_time,
                "response_length": len(response.content),
                "response_preview": response.content[:150] + "..."
            }
            
            print(f"✅ {model} 测试成功")
            print(f"⏱️ 响应时间: {results[model]['response_time']:.2f}秒")
            
        except Exception as e:
            results[model] = {
                "success": False,
                "error": str(e)
            }
            print(f"❌ {model} 测试失败: {e}")
    
    # 显示对比结果
    print("\n📊 模型对比结果:")
    print("=" * 50)
    for model, result in results.items():
        print(f"\n🧠 {model}:")
        if result["success"]:
            print(f"  响应时间: {result['response_time']:.2f}秒")
            print(f"  响应长度: {result['response_length']}字符")
            print(f"  响应预览: {result['response_preview']}")
        else:
            print(f"  状态: 失败 - {result['error']}")
    
    return True

def main():
    """主测试函数"""
    print("🚀 DeepSeek R1 模型集成测试")
    print("=" * 60)
    
    # 检查API密钥
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        print("❌ 错误: 未设置DEEPSEEK_API_KEY环境变量")
        print("请在.env文件中配置您的DeepSeek API密钥")
        return
    
    print(f"🔑 API密钥: {api_key[:8]}...{api_key[-4:]}")
    
    # 显示配置信息
    print("\n📋 当前配置:")
    from configs.deepseek_r1_config import print_r1_features, print_config_guide
    print_r1_features()
    print_config_guide()
    
    # 执行测试
    tests = [
        ("API连接测试", test_api_connection),
        ("推理能力测试", test_r1_reasoning),
        ("蒸馏版模型测试", test_distill_model),
        ("模型对比测试", test_model_comparison),
        ("完整分析测试", test_stock_analysis),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"🧪 {test_name}")
        print(f"{'='*60}")
        
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 显示测试总结
    print(f"\n{'='*60}")
    print("📊 测试总结")
    print(f"{'='*60}")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！DeepSeek R1集成成功！")
    else:
        print("⚠️ 部分测试失败，请检查配置和网络连接")

if __name__ == "__main__":
    main()
